package com.dipspro.modules.embedding.dto;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 中文句子转向量响应DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class EmbeddingResponse {

    /**
     * 单个向量结果（用于单文本转换）
     */
    private float[] vector;

    /**
     * 批量向量结果（用于批量转换）
     */
    private List<float[]> vectors;

    /**
     * 向量维度
     */
    private int dimension;

    /**
     * 处理的文本数量
     */
    private int textCount;

    /**
     * 处理时间（毫秒）
     */
    private long processingTimeMs;

    /**
     * 相似度矩阵（当请求包含多个文本且要求计算相似度时）
     */
    private double[][] similarityMatrix;

    /**
     * 是否异步处理
     */
    private boolean async;

    /**
     * 异步任务ID（仅在异步处理时返回）
     */
    private String taskId;

    /**
     * 错误信息
     */
    private String error;

    /**
     * 成功标识
     */
    private boolean success = true;

    /**
     * 创建单文本成功响应
     */
    public static EmbeddingResponse success(float[] vector, long processingTimeMs) {
        EmbeddingResponse response = new EmbeddingResponse();
        response.vector = vector;
        response.dimension = vector.length;
        response.textCount = 1;
        response.processingTimeMs = processingTimeMs;
        response.success = true;
        return response;
    }

    /**
     * 创建批量文本成功响应
     */
    public static EmbeddingResponse success(List<float[]> vectors, long processingTimeMs) {
        EmbeddingResponse response = new EmbeddingResponse();
        response.vectors = vectors;
        response.dimension = vectors.isEmpty() ? 0 : vectors.get(0).length;
        response.textCount = vectors.size();
        response.processingTimeMs = processingTimeMs;
        response.success = true;
        return response;
    }

    /**
     * 创建带相似度矩阵的成功响应
     */
    public static EmbeddingResponse success(List<float[]> vectors, double[][] similarityMatrix, long processingTimeMs) {
        EmbeddingResponse response = new EmbeddingResponse();
        response.vectors = vectors;
        response.dimension = vectors.isEmpty() ? 0 : vectors.get(0).length;
        response.textCount = vectors.size();
        response.similarityMatrix = similarityMatrix;
        response.processingTimeMs = processingTimeMs;
        response.success = true;
        return response;
    }

    /**
     * 创建异步响应
     */
    public static EmbeddingResponse async(String taskId) {
        EmbeddingResponse response = new EmbeddingResponse();
        response.async = true;
        response.taskId = taskId;
        response.success = true;
        return response;
    }

    /**
     * 创建错误响应
     */
    public static EmbeddingResponse error(String error) {
        EmbeddingResponse response = new EmbeddingResponse();
        response.error = error;
        response.success = false;
        return response;
    }
}
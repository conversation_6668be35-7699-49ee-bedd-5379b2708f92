<route lang="yaml">
meta:
  enabled: false
</route>

<script setup lang="ts">
defineOptions({
  name: 'FeatureExampleConfetti',
})

const confetti = useConfetti()

function handleClick1() {
  confetti({
    zIndex: 3000,
    particleCount: 100,
    spread: 70,
  })
}

function handleClick2() {
  const count = 200
  const defaults = {
    zIndex: 3000,
    origin: { y: 0.7 },
  }

  function fire(particleRatio: number, opts: any) {
    confetti({
      ...defaults,
      ...opts,
      particleCount: Math.floor(count * particleRatio),
    })
  }

  fire(0.25, {
    spread: 26,
    startVelocity: 55,
  })
  fire(0.2, {
    spread: 60,
  })
  fire(0.35, {
    spread: 100,
    decay: 0.91,
    scalar: 0.8,
  })
  fire(0.1, {
    spread: 120,
    startVelocity: 25,
    decay: 0.92,
    scalar: 1.2,
  })
  fire(0.1, {
    spread: 120,
    startVelocity: 45,
  })
}

function handleClick3() {
  const duration = 15 * 1000
  const animationEnd = Date.now() + duration
  const defaults = {
    zIndex: 3000,
    startVelocity: 30,
    spread: 360,
    ticks: 60,
  }

  function randomInRange(min: number, max: number) {
    return Math.random() * (max - min) + min
  }

  const interval = window.setInterval(() => {
    const timeLeft = animationEnd - Date.now()

    if (timeLeft <= 0) {
      return clearInterval(interval)
    }

    const particleCount = 50 * (timeLeft / duration)
    // 由于粒子会落下，起点稍微高一点比随机开始好一些
    confetti({
      ...defaults,
      particleCount,
      origin: { x: randomInRange(0.1, 0.3), y: Math.random() - 0.2 },
    })
    confetti({
      ...defaults,
      particleCount,
      origin: { x: randomInRange(0.7, 0.9), y: Math.random() - 0.2 },
    })
  }, 250)
}

function handleClick4() {
  const defaults = {
    zIndex: 3000,
    spread: 360,
    ticks: 50,
    gravity: 0,
    decay: 0.94,
    startVelocity: 30,
    colors: ['FFE400', 'FFBD00', 'E89400', 'FFCA6C', 'FDFFB8'],
  }

  function shoot() {
    confetti({
      ...defaults,
      particleCount: 40,
      scalar: 1.2,
      shapes: ['star'],
    })

    confetti({
      ...defaults,
      particleCount: 10,
      scalar: 0.75,
      shapes: ['circle'],
    })
  }

  setTimeout(shoot, 0)
  setTimeout(shoot, 100)
  setTimeout(shoot, 200)
}

function handleClick5() {
  const duration = 15 * 1000
  const animationEnd = Date.now() + duration
  let skew = 1

  function randomInRange(min: number, max: number) {
    return Math.random() * (max - min) + min
  }

  ;(function frame() {
    const timeLeft = animationEnd - Date.now()
    const ticks = Math.max(200, 500 * (timeLeft / duration))
    skew = Math.max(0.8, skew - 0.001)

    confetti({
      zIndex: 3000,
      particleCount: 1,
      startVelocity: 0,
      ticks,
      origin: {
        x: Math.random(),
        // 由于粒子向下坠落，倾斜开始朝向顶部
        y: Math.random() * skew - 0.2,
      },
      colors: ['#ffffff'],
      shapes: ['circle'],
      gravity: randomInRange(0.4, 0.6),
      scalar: randomInRange(0.4, 1),
      drift: randomInRange(-0.4, 0.4),
    })

    if (timeLeft > 0) {
      requestAnimationFrame(frame)
    }
  })()
}

function handleClick6() {
  const end = Date.now() + 15 * 1000

  const colors = ['#bb0000', '#ffffff']

  ;(function frame() {
    confetti({
      zIndex: 3000,
      particleCount: 2,
      angle: 60,
      spread: 55,
      origin: { x: 0, y: 0.5 },
      colors,
    })
    confetti({
      zIndex: 3000,
      particleCount: 2,
      angle: 120,
      spread: 55,
      origin: { x: 1, y: 0.5 },
      colors,
    })

    if (Date.now() < end) {
      requestAnimationFrame(frame)
    }
  })()
}

function handleClick7() {
  const scalar = 2
  const unicorn = confetti.shapeFromText({ text: '🦄', scalar })

  const defaults = {
    zIndex: 3000,
    spread: 360,
    ticks: 60,
    gravity: 0,
    decay: 0.96,
    startVelocity: 20,
    shapes: [unicorn],
    scalar,
  }

  function shoot() {
    confetti({
      ...defaults,
      particleCount: 30,
    })

    confetti({
      ...defaults,
      particleCount: 5,
      flat: true,
    })

    confetti({
      ...defaults,
      particleCount: 15,
      scalar: scalar / 2,
      shapes: ['circle'],
    })
  }

  setTimeout(shoot, 0)
  setTimeout(shoot, 100)
  setTimeout(shoot, 200)
}
</script>

<template>
  <div>
    <FaPageHeader title="五彩纸屑" description="在成功完成注册或付款等活动后使用的庆祝动画" />
    <FaPageMain main-class="flex gap-2">
      <FaButton @click="handleClick1">
        五彩纸屑
      </FaButton>
      <FaButton @click="handleClick2">
        逼真效果
      </FaButton>
      <FaButton @click="handleClick3">
        烟花
      </FaButton>
      <FaButton @click="handleClick4">
        星星
      </FaButton>
      <FaButton @click="handleClick5">
        雪花
      </FaButton>
      <FaButton @click="handleClick6">
        学院风
      </FaButton>
      <FaButton @click="handleClick7">
        Emoji
      </FaButton>
    </FaPageMain>
  </div>
</template>

<route lang="yaml">
meta:
  enabled: false
</route>

<script setup lang="ts">
import useMenuBadgeStore from '@/store/modules/menuBadge'

const menuBadgeStore = useMenuBadgeStore()

function plus() {
  menuBadgeStore.setNumber(menuBadgeStore.number + 1)
}

function minus() {
  menuBadgeStore.setNumber(menuBadgeStore.number - 1)
}
</script>

<template>
  <div>
    <FaPageHeader title="数字标记" description="搭配 Pinia 可实现动态设置。请控制数字展示长度，避免导航标记覆盖导航标题，为 0 时则隐藏" />
    <FaPageMain>
      <div class="space-y-2">
        <div>当前 badge 值：{{ menuBadgeStore.number }}</div>
        <div class="space-x-2">
          <FaButton @click="plus">
            <FaIcon name="i-ep:plus" />
            1
          </FaButton>
          <FaButton @click="minus">
            <FaIcon name="i-ep:minus" />
            1
          </FaButton>
        </div>
      </div>
    </FaPageMain>
  </div>
</template>

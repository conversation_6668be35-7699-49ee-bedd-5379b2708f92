package com.dipspro.modules.profile.service;

import java.util.List;

import com.dipspro.modules.profile.dto.MobileCombinedStatsDTO;
import com.dipspro.modules.profile.dto.ProjectDetail;

/**
 * 项目数据丰富化服务接口
 * 
 * <AUTHOR>
 * @since 2025-06-03 14:51:45
 */
public interface ProjectEnrichmentService {

    /**
     * 为项目列表丰富所有数据
     * 
     * @param projects      项目列表
     * @param mobile        手机号
     * @param combinedStats 综合统计数据
     */
    void enrichProjectsWithAllData(List<ProjectDetail> projects, String mobile, MobileCombinedStatsDTO combinedStats);

    /**
     * 为项目列表丰富链家数据
     * 
     * @param projects 项目列表
     */
    void enrichProjectsWithLianjiaData(List<ProjectDetail> projects);

    /**
     * 为项目列表丰富归一化数据
     * 
     * @param projects 项目列表
     * @param mobile   手机号
     */
    void enrichProjectsWithNormalizedData(List<ProjectDetail> projects, String mobile);
}
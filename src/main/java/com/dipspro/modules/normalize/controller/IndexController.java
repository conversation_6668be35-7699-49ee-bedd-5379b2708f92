package com.dipspro.modules.normalize.controller;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.dipspro.common.dto.ApiResponse;
import com.dipspro.modules.chat.entity.IndexDefinition;
import com.dipspro.modules.normalize.service.IndexService;
import com.dipspro.modules.profile.dto.IndexHierarchyDto;

import lombok.extern.slf4j.Slf4j;

/**
 * 指标控制器
 * 提供查询指标定义的REST API
 */
@Slf4j
@RestController
@RequestMapping("/api/indexes")
public class IndexController {

    private final IndexService indexService;

    @Autowired
    public IndexController(IndexService indexService) {
        this.indexService = indexService;
    }

    /**
     * 获取所有指标定义
     * @return 所有指标定义列表
     */
    @GetMapping
    public ApiResponse<List<IndexDefinition>> getAllIndexes() {
        try {
            List<IndexDefinition> indexes = indexService.getAllIndexDefinitions();
            return ApiResponse.success(indexes, "获取所有指标定义成功");
        } catch (Exception e) {
            log.error("获取所有指标定义失败", e);
            return ApiResponse.error("获取所有指标定义失败");
        }
    }

    /**
     * 获取所有主指标
     * @return 主指标列表
     */
    @GetMapping("/main")
    public ApiResponse<List<IndexDefinition>> getMainIndexes() {
        try {
            List<IndexDefinition> mainIndexes = indexService.getAllMainIndexes();
            return ApiResponse.success(mainIndexes, "获取所有主指标成功");
        } catch (Exception e) {
            log.error("获取所有主指标失败", e);
            return ApiResponse.error("获取所有主指标失败");
        }
    }

    /**
     * 根据planar指标代码查询指标定义
     * @param code planar指标代码
     * @return 指标定义
     */
    @GetMapping("/planar/{code}")
    public ApiResponse<IndexDefinition> getIndexByPlanarCode(@PathVariable("code") String code) {
        try {
            Optional<IndexDefinition> index = indexService.getIndexByPlanarCode(code);
            if (index.isPresent()) {
                return ApiResponse.success(index.get(), "查询指标定义成功");
            } else {
                return ApiResponse.error("指标定义不存在");
            }
        } catch (Exception e) {
            log.error("根据planar代码查询指标定义失败: {}", code, e);
            return ApiResponse.error("查询指标定义失败");
        }
    }

    /**
     * 根据legacy指标代码查询指标定义
     * @param code legacy指标代码
     * @return 指标定义
     */
    @GetMapping("/legacy/{code}")
    public ApiResponse<IndexDefinition> getIndexByLegacyCode(@PathVariable("code") String code) {
        try {
            Optional<IndexDefinition> index = indexService.getIndexByLegacyCode(code);
            if (index.isPresent()) {
                return ApiResponse.success(index.get(), "查询指标定义成功");
            } else {
                return ApiResponse.error("指标定义不存在");
            }
        } catch (Exception e) {
            log.error("根据legacy代码查询指标定义失败: {}", code, e);
            return ApiResponse.error("查询指标定义失败");
        }
    }

    /**
     * 获取指定主指标的所有子指标
     * @param mainCode 主指标代码
     * @return 子指标列表
     */
    @GetMapping("/main/{mainCode}/sub")
    public ApiResponse<List<IndexDefinition>> getSubIndexes(@PathVariable("mainCode") String mainCode) {
        try {
            List<IndexDefinition> subIndexes = indexService.getSubIndexes(mainCode);
            return ApiResponse.success(subIndexes, "获取子指标成功");
        } catch (Exception e) {
            log.error("获取子指标失败: {}", mainCode, e);
            return ApiResponse.error("获取子指标失败");
        }
    }

    /**
     * 获取主指标与子指标的映射关系
     * @return 主指标代码 -> 子指标列表的映射
     */
    @GetMapping("/mapping")
    public ApiResponse<Map<String, List<IndexDefinition>>> getMainToSubMapping() {
        try {
            Map<String, List<IndexDefinition>> mapping = indexService.getMainToSubIndexMapping();
            return ApiResponse.success(mapping, "获取指标映射关系成功");
        } catch (Exception e) {
            log.error("获取指标映射关系失败", e);
            return ApiResponse.error("获取指标映射关系失败");
        }
    }

    /**
     * 获取所有指标的层级结构
     * @return 指标层级结构列表
     */
    @GetMapping("/hierarchy")
    public ApiResponse<List<IndexHierarchyDto>> getIndexHierarchy() {
        try {
            // 获取所有主指标
            List<IndexDefinition> mainIndexes = indexService.getAllMainIndexes();
            
            // 构建层级结构
            List<IndexHierarchyDto> hierarchy = new ArrayList<>();
            
            for (IndexDefinition mainIndex : mainIndexes) {
                List<IndexDefinition> subIndexes = indexService.getSubIndexes(mainIndex.getPlanarIndexCode());
                
                hierarchy.add(IndexHierarchyDto.builder()
                        .mainIndex(mainIndex)
                        .subIndexes(subIndexes)
                        .subIndexCount(subIndexes.size())
                        .build());
            }
            
            return ApiResponse.success(hierarchy, "获取指标层级结构成功");
        } catch (Exception e) {
            log.error("获取指标层级结构失败", e);
            return ApiResponse.error("获取指标层级结构失败");
        }
    }
    
    /**
     * 获取指定主指标的层级结构
     * @param mainCode 主指标代码
     * @return 指标层级结构
     */
    @GetMapping("/hierarchy/{mainCode}")
    public ApiResponse<IndexHierarchyDto> getIndexHierarchyByMainCode(@PathVariable("mainCode") String mainCode) {
        try {
            // 获取主指标
            Optional<IndexDefinition> mainIndexOpt = indexService.getIndexByPlanarCode(mainCode);
            
            if (mainIndexOpt.isEmpty() || !mainIndexOpt.get().isMainIndex()) {
                return ApiResponse.error("主指标不存在或不是主指标");
            }
            
            IndexDefinition mainIndex = mainIndexOpt.get();
            List<IndexDefinition> subIndexes = indexService.getSubIndexes(mainIndex.getPlanarIndexCode());
            
            IndexHierarchyDto hierarchy = IndexHierarchyDto.builder()
                    .mainIndex(mainIndex)
                    .subIndexes(subIndexes)
                    .subIndexCount(subIndexes.size())
                    .build();
            
            return ApiResponse.success(hierarchy, "获取指标层级结构成功");
        } catch (Exception e) {
            log.error("获取指定主指标的层级结构失败: {}", mainCode, e);
            return ApiResponse.error("获取指标层级结构失败");
        }
    }
} 
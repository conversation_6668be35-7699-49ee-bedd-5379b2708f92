# =======================================
# Test Profile Configuration
# =======================================

spring:
  # 测试环境使用 H2 内存数据库
  datasource:
    url: ${SPRING_DATASOURCE_URL:************************************************} # 使用环境变量，提供默认值
    username: ${SPRING_DATASOURCE_USERNAME:prod_user} # 使用环境变量
    password: ${SPRING_DATASOURCE_PASSWORD:prod_password} # 使用环境变量
  jpa:
    # 针对 H2 的方言
    database-platform: org.hibernate.dialect.H2Dialect
    hibernate:
      # 测试时每次重新创建 schema
      ddl-auto: create-drop
    # 测试时也可以显示 SQL
    show-sql: true
    properties:
      hibernate:
        format_sql: true

# 测试环境一般不需要 Redis，如果需要可以单独配置

# 测试环境日志级别 (示例)
logging:
  level:
    com.dipspro: INFO
    org.springframework.web: INFO
    org.hibernate.SQL: DEBUG # 测试时可能仍需查看 SQL

n8n:
  auth:
    username: dipspro
    password: L4NshqmpVKVL6fpZ8GDK
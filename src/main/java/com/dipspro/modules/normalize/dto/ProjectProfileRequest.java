package com.dipspro.modules.normalize.dto;

import java.util.Map;

import com.dipspro.modules.profile.dto.PromptTemplateDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 楼盘画像请求数据传输对象
 * 承载projectProfile接口的所有参数，包括template相关参数
 * 
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProjectProfileRequest {

    /**
     * 楼盘名称
     */
    private String name;

    /**
     * 插槽参数
     */
    private Map<String, Object> slots;

    /**
     * 代理类型
     */
    private String agentType;

    /**
     * 聊天输入
     */
    private String chatInput;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 模板ID
     */
    private String templateId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户名
     */
    private String username;

    private PromptTemplateDto promptTemplateDto;
}
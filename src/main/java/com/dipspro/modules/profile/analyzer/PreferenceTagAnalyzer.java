package com.dipspro.modules.profile.analyzer;

import static com.dipspro.constant.ProjectStatsConstants.PREFERRED_CATEGORY_THRESHOLD;
import static com.dipspro.constant.ProjectStatsConstants.TAG_DECORATION_PREFERENCE;
import static com.dipspro.constant.ProjectStatsConstants.TAG_DESIGN_PREFERENCE;
import static com.dipspro.constant.ProjectStatsConstants.TAG_FACILITY_PREFERENCE;
import static com.dipspro.constant.ProjectStatsConstants.TAG_HIGH_STANDARD;
import static com.dipspro.constant.ProjectStatsConstants.TAG_PROMISE_FULFILLMENT;
import static com.dipspro.constant.ProjectStatsConstants.TAG_PROPERTY_SERVICE;
import static com.dipspro.constant.ProjectStatsConstants.TAG_QUALITY_PREFERENCE;
import static com.dipspro.constant.ProjectStatsConstants.TAG_TYPE_LIVING_PREFERENCE;
import static com.dipspro.constant.ProjectStatsConstants.TAG_TYPE_PURCHASE_DECISION;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;

import com.dipspro.modules.normalize.dto.NormalizeData;
import com.dipspro.modules.profile.dto.ProjectDetail;
import com.dipspro.modules.profile.dto.UserProfileTag;
import com.dipspro.util.MapUtils;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.extern.slf4j.Slf4j;

/**
 * 偏好标签分析器 - 处理各种偏好相关标签
 *
 * <AUTHOR>
 */
@Slf4j
public class PreferenceTagAnalyzer extends UserProfileAnalyzer {

    /**
     * 构造函数
     *
     * @param projects        项目列表
     * @param userProfileTags 用户画像标签列表
     */
    public PreferenceTagAnalyzer(List<ProjectDetail> projects, List<UserProfileTag> userProfileTags) {
        super(projects, userProfileTags);
    }

    @Override
    protected void doAnalyze(StringRedisTemplate redisTemplate) {
        // 实现所有分析方法
        analyzeFacilityPreferenceTag(redisTemplate);
        analyzeDesignPreferenceTag();
        analyzeQualityPreferenceTag();
        analyzeDecorationPreferenceTag();
        analyzePropertyServicePreferenceTag();
        analyzePromiseFulfillmentTag();
        analyzeHighStandardPreferenceTag();
    }

    /**
     * 分析配套设施偏好标签
     * 标签规则：namelist中有房产，且每一套房产的7类配套数量（公交站\商场\医院\超市\幼小中学\地铁\公园）中均有50%（如7类中的4类）好于城市均值
     */
    public void analyzeFacilityPreferenceTag(StringRedisTemplate redisTemplate) {
        if (projects.isEmpty()) {
            return;
        }

        List<ProjectDetail> matchedProjects = new ArrayList<>();
        int projectsWithFacilities = 0;

        log.info("开始分析配套设施偏好标签，共{}个项目", projects.size());

        // 引入Jackson ObjectMapper解析JSON
        ObjectMapper objectMapper = new ObjectMapper();

        for (ProjectDetail project : projects) {
            // 取项目所在的城市
            String cityName = project.getCity();
            if (StringUtils.isBlank(cityName)) {
                log.debug("项目[{}]城市信息为空，跳过分析", project.getProjectName());
                continue;
            }

            // 检查项目是否有配套设施数据
            if (!project.hasAnyFacilities()) {
                log.debug("项目[{}]无配套设施数据，跳过分析", project.getProjectName());
                continue;
            }

            projectsWithFacilities++;

            // 读取项目中的各类配套设施数量
            Integer mallAmount = project.getMallAmount();
            Integer supermarketAmount = project.getSupermarketAmount();
            Integer hospitalAmount = project.getHospitalAmount();
            Integer parkAmount = project.getParkAmount();
            Integer schoolAmount = project.getSchoolAmount();
            Integer subwayStationAmount = project.getSubwayStationAmount();
            Integer busStopAmount = project.getBusStopAmount();

            // 构建Redis键，查询城市均值数据
            String redisKey = "城市总体项目配套均值:" + cityName;

            // 初始化城市均值默认值
            Double cityMallAvg = null;
            Double citySupermarketAvg = null;
            Double cityHospitalAvg = null;
            Double cityParkAvg = null;
            Double citySchoolAvg = null;
            Double citySubwayAvg = null;
            Double cityBusStopAvg = null;

            try {
                // 从Redis获取城市均值数据
                String cityAvgJson = redisTemplate.opsForValue().get(redisKey);

                if (StringUtils.isNotBlank(cityAvgJson)) {
                    log.debug("成功从Redis获取城市[{}]配套均值数据", cityName);

                    // 解析JSON数据
                    Map<String, Object> cityAvgMap = objectMapper.readValue(cityAvgJson, java.util.Map.class);

                    // 从Map中提取各类配套设施的均值
                    cityMallAvg = MapUtils.safeGetDouble(cityAvgMap, "mall_avg");
                    citySupermarketAvg = MapUtils.safeGetDouble(cityAvgMap, "supermarket_avg");
                    cityHospitalAvg = MapUtils.safeGetDouble(cityAvgMap, "hospital_avg");
                    cityParkAvg = MapUtils.safeGetDouble(cityAvgMap, "park_avg");
                    citySchoolAvg = MapUtils.safeGetDouble(cityAvgMap, "school_avg");
                    citySubwayAvg = MapUtils.safeGetDouble(cityAvgMap, "subway_station_avg");
                    cityBusStopAvg = MapUtils.safeGetDouble(cityAvgMap, "bus_stop_avg");
                }
            } catch (Exception e) {
                log.error("获取城市[{}]配套均值数据出错: {}", cityName, e.getMessage());
            }

            // 日志记录当前项目配套设施数量与城市均值的对比
            if (log.isDebugEnabled()) {
                log.debug("项目[{}]配套设施情况：", project.getProjectName());
                log.debug("  购物中心: {} (城市均值: {})", mallAmount, cityMallAvg);
                log.debug("  超市: {} (城市均值: {})", supermarketAmount, citySupermarketAvg);
                log.debug("  医院: {} (城市均值: {})", hospitalAmount, cityHospitalAvg);
                log.debug("  公园: {} (城市均值: {})", parkAmount, cityParkAvg);
                log.debug("  学校: {} (城市均值: {})", schoolAmount, citySchoolAvg);
                log.debug("  地铁站: {} (城市均值: {})", subwayStationAmount, citySubwayAvg);
                log.debug("  公交站: {} (城市均值: {})", busStopAmount, cityBusStopAvg);
            }

            // 统计有效的设施指标数量（非空值）
            int validFacilityCount = 0;
            // 计算项目配套设施超过城市均值的数量
            int betterThanAvgCount = 0;

            if (mallAmount != null && cityMallAvg != null) {
                validFacilityCount++;
                if (mallAmount > cityMallAvg) {
                    betterThanAvgCount++;
                }
            }

            if (supermarketAmount != null && citySupermarketAvg != null) {
                validFacilityCount++;
                if (supermarketAmount > citySupermarketAvg) {
                    betterThanAvgCount++;
                }
            }

            if (hospitalAmount != null && cityHospitalAvg != null) {
                validFacilityCount++;
                if (hospitalAmount > cityHospitalAvg) {
                    betterThanAvgCount++;
                }
            }

            if (parkAmount != null && cityParkAvg != null) {
                validFacilityCount++;
                if (parkAmount > cityParkAvg) {
                    betterThanAvgCount++;
                }
            }

            if (schoolAmount != null && citySchoolAvg != null) {
                validFacilityCount++;
                if (schoolAmount > citySchoolAvg) {
                    betterThanAvgCount++;
                }
            }

            if (subwayStationAmount != null && citySubwayAvg != null) {
                validFacilityCount++;
                if (subwayStationAmount > citySubwayAvg) {
                    betterThanAvgCount++;
                }
            }

            if (busStopAmount != null && cityBusStopAvg != null) {
                validFacilityCount++;
                if (busStopAmount > cityBusStopAvg) {
                    betterThanAvgCount++;
                }
            }

            // 确保至少有4个有效的设施指标
            if (validFacilityCount < 4) {
                log.debug("项目[{}]有效设施指标数量不足4个，跳过分析", project.getProjectName());
                continue;
            }

            // 计算优于城市均值的设施占比
            double betterRatio = (double) betterThanAvgCount / validFacilityCount;
            log.debug("项目[{}]配套设施优于城市均值的比例: {}/{} = {}",
                    project.getProjectName(), betterThanAvgCount, validFacilityCount, betterRatio);

            // 如果超过50%的配套设施数量高于城市均值，则认为该项目符合条件
            if (betterRatio >= 0.5) {
                matchedProjects.add(project);
                log.info("项目[{}]符合配套设施偏好条件，优于城市均值的设施比例: {}",
                        project.getProjectName(), betterRatio);
            }
        }

        // 检查是否符合打标签条件：
        // 1. 每一套房产的7类配套数量均好于城市均值
        if (projectsWithFacilities > 0 &&
                matchedProjects.size() == projectsWithFacilities) {

            log.info("满足配套设施偏好标签条件: 共{}个具有设施数据的项目中的{}个项目符合条件",
                    projectsWithFacilities, matchedProjects.size());

            UserProfileTag tag = UserProfileTag.create(
                    TAG_FACILITY_PREFERENCE,
                    "配套设施偏好",
                    "客户购房决策中较为看重配套设施和地理位置",
                    TAG_TYPE_PURCHASE_DECISION
            );

            // 添加匹配项目，并记录日志
            StringBuilder matchedInfo = new StringBuilder();
            for (ProjectDetail project : matchedProjects) {
                if (StringUtils.isNotBlank(project.getCustomerProject()) &&
                        StringUtils.isNotBlank(project.getProjectName())) {
                    tag.addMatchedProject(project.getCustomerProject(), project.getProjectName());

                    if (!matchedInfo.isEmpty()) {
                        matchedInfo.append(", ");
                    }
                    matchedInfo.append(project.getProjectName());
                }
            }

            log.info("创建配套设施偏好标签，匹配项目: {}", matchedInfo);
            addTag(tag);
        } else {
            log.info("不满足配套设施偏好标签条件: 共{}个项目中的{}个项目符合条件",
                    projectsWithFacilities, matchedProjects.size());
        }
    }

    /**
     * 分析设计偏好标签
     * 标签规则：
     * 先汇总所有项目的 rawData, 以 rawData 集合为单位做出处理的数据源，不区分项目
     * 1. dataset数据中rk0a5有评价的数据中1-3分评价占比多于50% or 开放题有内容的数据中50%以上提及'设计'（开放题关键字匹配）
     * 1.1. dataset 数据在ProjectDetail的rawData中，遍历所有rawData，找到rk0a5，然后判断是否存在1-3分评价，或者开放题中是否存在关键字"设计"
     * 1.2. 暂不实现：开放题有内容的数据中50%以上提及'设计'（807子触点/730101005003/308008/303008）
     * 2. 比如dataset共有10条，其中6条rk0a5有值，其中4条rk0a5是1-3分评价，占比为4/6 >  50%
     * 3. 开放题需要找到对应的文本是否存在关键字"设计"，看是否有提及
     * 暂不实现：4. 开放题统计结果查询：lookup on customerTextStat where customerTextStat.dataPeriod == '2024-0112'（这里看的是开放题统计信息，有三类cityTextStat\customerCityTextStat\customerTextStat）
     *
     * <p>
     * rk0a5低分测试数据：15900267939
     * 开放题关键字测试数据：15995834213
     */
    public void analyzeDesignPreferenceTag() {
        String[] keywords = {"设计"};
        String[] indexCodes = {"rk0a5"};
        analyzeRawDataBasedPreferenceTag(
                indexCodes, 
                keywords, 
                TAG_DESIGN_PREFERENCE,
                "设计偏好",
                "客户购房决策中较为看重设计感和空间布局"
        );
    }

    /**
     * 分析质量做工偏好标签
     * 标签规则：
     * 1. 先汇总所有项目的 rawData, 以 rawData 集合为单位做出处理的数据源，不区分项目
     * 2. dataset数据中re0a5有评价的数据中1-3分评价占比多于50% or 开放题有内容的数据中50%以上提及'质量'（开放题关键字匹配）
     *   2.1. dataset 数据在ProjectDetail的rawData中，遍历所有rawData，找到re0a5，然后判断是否存在1-3分评价，或者开放题中是否存在关键字"质量"
     *   2.2. 暂不实现：开放题有内容的数据中50%以上提及'质量'（819）
     * 3. 比如dataset共有10条，其中6条re0a5有值，其中4条re0a5是1-3分评价，占比为4/6 >  50%
     * 4. 开放题需要在rawData 集合中计算存在关键字"质量"的数量，计算比例，看是否有提及
     * 暂不实现：5. 开放题统计结果查询：lookup on customerTextStat where customerTextStat.dataPeriod == '2024-0112'（这里看的是开放题统计信息，有三类cityTextStat\customerCityTextStat\customerTextStat）
     *
     * <p>
     * re0a5低分测试数据：13365290191
     * 开放题关键字测试数据：17327768881
     */
    public void analyzeQualityPreferenceTag() {
        String[] keywords = {"质量"};
        String[] indexCodes = {"re0a5"};
        analyzeRawDataBasedPreferenceTag(
                indexCodes, 
                keywords, 
                TAG_QUALITY_PREFERENCE,
                "品质偏好",
                "客户购房决策中较为看重建筑用料和品质工艺"
        );
    }
    
    /**
     * 基于原始数据分析客户偏好标签
     * 此方法是分析客户偏好标签的共通处理逻辑，支持多个指标代码
     *
     * @param indexCodes 指标Code数组，如{"rk0a5"}或{"re0a5"}或{"rq03a5", "rq0a5"}
     * @param keywords 关键词数组，用于在开放题中匹配，如{"设计"}或{"质量"}
     * @param tagCode 标签代码
     * @param tagName 标签名称
     * @param tagDescription 标签描述
     */
    private void analyzeRawDataBasedPreferenceTag(String[] indexCodes, String[] keywords, String tagCode, String tagName, String tagDescription) {
         if (projects.isEmpty()) {
            return;
        }

        List<ProjectDetail> matchedProjects = new ArrayList<>();
        List<NormalizeData> allRawData = new ArrayList<>();

        // 第一步：汇总所有项目的rawData
        for (ProjectDetail project : projects) {
            if (project.getRawData() != null && !project.getRawData().isEmpty()) {
                allRawData.addAll(project.getRawData());
            }
        }

        log.info("汇总所有项目的rawData，共{}条记录", allRawData.size());

        // 第二步：基于汇总后的rawData进行分析 - 检查评分字段情况和开放题关键字
        boolean matchedByIndex = false;
        // 使用Map存储每个指标的评分计数
        Map<String, Integer> totalRatingCountMap = new java.util.HashMap<>();
        Map<String, Integer> lowScoreRatingCountMap = new java.util.HashMap<>();
        
        // 初始化计数器
        for (String indexCode : indexCodes) {
            totalRatingCountMap.put(indexCode, 0);
            lowScoreRatingCountMap.put(indexCode, 0);
        }
        
        boolean matchedByText = false;
        int textCount = 0; // 开放题有内容的记录数
        int effectiveCount = 0; // 开放题中提及关键词的记录数

        for (NormalizeData data : allRawData) {
            Map<String, Object> dataItem = data.getData();
            if (null == dataItem || dataItem.isEmpty()) continue;

            // 获取各个评分字段值
            for (String indexCode : indexCodes) {
                Object ratingValue = dataItem.get(indexCode);
    
                if (ratingValue != null) {
                    totalRatingCountMap.put(indexCode, totalRatingCountMap.get(indexCode) + 1);
    
                    // 尝试将评分转换为数值并检查是否为1-3分
                    try {
                        int score = Integer.parseInt(ratingValue.toString());
                        if (score >= 1 && score <= 3) {
                            lowScoreRatingCountMap.put(indexCode, lowScoreRatingCountMap.get(indexCode) + 1);
    
                            for (ProjectDetail project : projects) {
                                if (project.getCustomerProject().equals(dataItem.getOrDefault("customer_project", ""))) {
                                    matchedProjects.add(project);
                                }
                            }
                        }
                    } catch (NumberFormatException e) {
                        // 忽略非数字值
                        log.debug("rawData中{}字段值[{}]无法转换为整数", indexCode, ratingValue);
                    }
                }
            }

            String textGood = dataItem.getOrDefault("open_text_good_1", "") + "",
                   textBad = dataItem.getOrDefault("open_text_bad_1", "") + "";
            if (StringUtils.isNotBlank(textGood) && StringUtils.isNotBlank(textBad)
                    && !textGood.equalsIgnoreCase("null") && !textBad.equalsIgnoreCase("null")) {
                textCount++;
                
                // 检查开放题中是否包含任一关键词
                boolean containsKeyword = false;
                for (String keyword : keywords) {
                    if (textGood.contains(keyword) || textBad.contains(keyword)) {
                        containsKeyword = true;
                        break;
                    }
                }
                
                if (containsKeyword) {
                    effectiveCount++;
                    for (ProjectDetail project : projects) {
                        if (project.getCustomerProject().equals(dataItem.getOrDefault("customer_project", ""))) {
                            matchedProjects.add(project);
                        }
                    }
                }
            }
        }

        // 检查每个指标的低分占比，只要有一个指标满足条件就匹配
        for (String indexCode : indexCodes) {
            int totalRatingCount = totalRatingCountMap.get(indexCode);
            int lowScoreRatingCount = lowScoreRatingCountMap.get(indexCode);
            
            // 如果有评分数据，计算低分占比
            if (totalRatingCount > 0) {
                double lowScoreRatio = (double) lowScoreRatingCount / totalRatingCount;
                log.debug("所有项目{}低分占比: {}/{} = {}", indexCode, lowScoreRatingCount, totalRatingCount, lowScoreRatio);
    
                if (lowScoreRatio >= 0.5) {
                    matchedByIndex = true;
                    log.info("基于所有项目的{}分析：低分占比{}%超过50%，{}标签条件已满足", indexCode, lowScoreRatio * 100, tagName);
                    break; // 一个指标满足条件就跳出循环
                }
            }
        }

        // 如果有开放题数据，计算提及关键词的占比
        if (textCount > 0) {
            double effectiveRatio = (double) effectiveCount / textCount;
            log.debug("所有项目开放题提及关键词占比: {}/{} = {}", effectiveCount, textCount, effectiveRatio);
            if (effectiveRatio >= 0.5) {
                matchedByText = true;
                log.info("基于所有项目的开放题分析：提及关键词占比{}%超过50%，{}标签条件已满足", effectiveRatio * 100, tagName);
            }
        }

        if (matchedByIndex || matchedByText) {
            UserProfileTag tag = UserProfileTag.create(
                    tagCode,
                    tagName,
                    tagDescription,
                    TAG_TYPE_PURCHASE_DECISION
            );

            // 添加匹配项目
            for (ProjectDetail project : matchedProjects) {
                if (StringUtils.isNotBlank(project.getCustomerProject()) &&
                        StringUtils.isNotBlank(project.getProjectName())) {
                    tag.addMatchedProject(project.getCustomerProject(), project.getProjectName());
                }
            }

            addTag(tag);
            log.info("添加{}标签，共有{}个匹配项目", tagName, matchedProjects.size());
        } else {
            log.info("没有项目满足{}标签条件，不添加标签", tagName);
        }
    }

    /**
     * 分析装修品质偏好标签
     * 标签规则：
     * 1. 先汇总所有项目的 rawData, 以 rawData 集合为单位做出处理的数据源，不区分项目
     * 2. dataset数据中rq03a5有评价的数据中1-3分评价占比多于50% or dataset数据中rq0a5有评价的数据中1-3分评价占比多于50% or 开放题有内容的数据中50%以上提及'装修'（开放题关键字匹配）
     *   2.1. dataset 数据在ProjectDetail的rawData中，遍历所有rawData，找到rq03a5，然后判断是否存在1-3分评价
     *   2.2. dataset 数据在ProjectDetail的rawData中，遍历所有rawData，找到rq0a5，然后判断是否存在1-3分评价
     *   2.3. 或者开放题中是否存在关键字"装修"
     *   2.4. 暂不实现：开放题有内容的数据中50%以上提及'装修'（813）
     * 3. 比如dataset共有10条，其中6条rq03a5有值，其中4条rq03a5是1-3分评价，占比为4/6 >  50%
     * 4. 开放题需要在rawData 集合中计算存在关键字"装修"的数量，计算比例，看是否有提及
     * 暂不实现：5. 开放题统计结果查询：lookup on customerTextStat where customerTextStat.dataPeriod == '2024-0112'（这里看的是开放题统计信息，有三类cityTextStat\customerCityTextStat\customerTextStat）
     *
     * <p>
     * rq03a5、rq0a5低分测试数据：13365290191
     * 开放题关键字测试数据：13681986953
     */
    public void analyzeDecorationPreferenceTag() {
        String[] keywords = {"装修", "精装", "豪装", "简装", "中装", "装饰", "内饰"};
        String[] indexCodes = {"rq03a5", "rq0a5"};
        analyzeRawDataBasedPreferenceTag(
                indexCodes,
                keywords,
                TAG_DECORATION_PREFERENCE,
                "装修偏好",
                "客户购房决策中较为看重装修质量和风格"
        );
    }

    /**
     * 分析物业服务偏好标签
     * 标签规则：首置首改项目物业费大于3元 or 改善项目物业费大于5元 or 高端项目物业费大于8元 or dataset中rt0a5有评价的数据中1-3分评价占比多于50%
     * 1. 首置首改物业费大于3元：buildingGrade=首置首改 and propertyCostLevel in ('3至4元','4至5元','5至8元','8至12元','12元以上')
     * 2. 改善项目物业费大于5元：buildingGrade=再改 and propertyCostLevel in ('5至8元','8至12元','12元以上')
     * 3. 高端项目物业费大于8元：buildingGrade=高端 and propertyCostLevel in ('8至12元','12元以上')
     * 4. dataset中rt0a5有评价的数据中1-3分评价占比多于50%：rt0a5有评价，且评价为1-3分
     */
    public void analyzePropertyServicePreferenceTag() {
        if (projects.isEmpty()) {
            return;
        }
        
        log.info("开始分析物业服务偏好标签，共{}个项目", projects.size());
        
        List<ProjectDetail> matchedProjects = new ArrayList<>();
        
        // 1. 检查每个项目的物业费和楼盘档次
        for (ProjectDetail project : projects) {
            String buildingGrade = project.getBuildingGrade();
            String propertyCostLevel = project.getPropertyCostLevel();
            
            if (StringUtils.isBlank(buildingGrade) || StringUtils.isBlank(propertyCostLevel)) {
                log.debug("项目[{}]楼盘档次或物业费信息为空，跳过物业费分析", project.getProjectName());
                continue;
            }
            
            log.debug("项目[{}]楼盘档次：{}，物业费等级：{}", project.getProjectName(), buildingGrade, propertyCostLevel);
            
            boolean matched = false;
            
            // 1. 首置首改物业费大于3元
            if ("首置首改".equals(buildingGrade) && 
                (propertyCostLevel.contains("3至4元") || 
                 propertyCostLevel.contains("4至5元") || 
                 propertyCostLevel.contains("5至8元") || 
                 propertyCostLevel.contains("8至12元") || 
                 propertyCostLevel.contains("12元以上"))) {
                
                matched = true;
                log.info("项目[{}]符合首置首改物业费大于3元条件", project.getProjectName());
            }
            
            // 2. 改善项目物业费大于5元
            if ("再改".equals(buildingGrade) && 
                (propertyCostLevel.contains("5至8元") || 
                 propertyCostLevel.contains("8至12元") || 
                 propertyCostLevel.contains("12元以上"))) {
                
                matched = true;
                log.info("项目[{}]符合改善项目物业费大于5元条件", project.getProjectName());
            }
            
            // 3. 高端项目物业费大于8元
            if ("高端".equals(buildingGrade) && 
                (propertyCostLevel.contains("8至12元") || 
                 propertyCostLevel.contains("12元以上"))) {
                
                matched = true;
                log.info("项目[{}]符合高端项目物业费大于8元条件", project.getProjectName());
            }
            
            if (matched && !matchedProjects.contains(project)) {
                matchedProjects.add(project);
            }
        }
        
        // 4. 分析dataset中rt0a5的评价数据
        List<NormalizeData> allRawData = new ArrayList<>();
        
        // 汇总所有项目的rawData
        for (ProjectDetail project : projects) {
            if (project.getRawData() != null && !project.getRawData().isEmpty()) {
                allRawData.addAll(project.getRawData());
            }
        }
        
        int totalRt0a5Count = 0;     // rt0a5有评价的数据数量
        int lowScoreRt0a5Count = 0;  // rt0a5为1-3分的数据数量
        
        for (NormalizeData data : allRawData) {
            Map<String, Object> dataItem = data.getData();
            if (null == dataItem || dataItem.isEmpty()) continue;
            
            // 获取rt0a5字段值
            Object ratingValue = dataItem.get("rt0a5");
            
            if (ratingValue != null) {
                totalRt0a5Count++;
                
                // 尝试将评分转换为数值并检查是否为1-3分
                try {
                    int score = Integer.parseInt(ratingValue.toString());
                    if (score >= 1 && score <= 3) {
                        lowScoreRt0a5Count++;
                        
                        // 找到对应项目并添加到匹配列表
                        String customerProject = (String)dataItem.getOrDefault("customer_project", "");
                        if (StringUtils.isNotBlank(customerProject)) {
                            for (ProjectDetail project : projects) {
                                if (customerProject.equals(project.getCustomerProject()) && 
                                        !matchedProjects.contains(project)) {
                                    matchedProjects.add(project);
                                    log.debug("项目[{}]符合rt0a5低分条件", project.getProjectName());
                                }
                            }
                        }
                    }
                } catch (NumberFormatException e) {
                    // 忽略非数字值
                    log.debug("rawData中rt0a5字段值[{}]无法转换为整数", ratingValue);
                }
            }
        }
        
        // 计算rt0a5低分占比
        boolean matchedByRt0a5 = false;
        if (totalRt0a5Count > 0) {
            double lowScoreRatio = (double) lowScoreRt0a5Count / totalRt0a5Count;
            log.info("rt0a5低分占比: {}/{} = {}", lowScoreRt0a5Count, totalRt0a5Count, lowScoreRatio);
            
            if (lowScoreRatio >= 0.5) {
                matchedByRt0a5 = true;
                log.info("满足rt0a5低分占比大于50%条件");
            }
        }
        
        // 判断是否满足标签条件：只要有一个条件满足即可创建标签
        if (!matchedProjects.isEmpty() || matchedByRt0a5) {
            log.info("满足物业服务偏好标签条件，共匹配{}个项目", matchedProjects.size());
            
            UserProfileTag tag = UserProfileTag.create(
                    TAG_PROPERTY_SERVICE,
                    "物业服务偏好",
                    "客户购房决策中较为看重物业服务质量",
                    TAG_TYPE_PURCHASE_DECISION
            );
            
            // 添加匹配项目
            StringBuilder matchedInfo = new StringBuilder();
            for (ProjectDetail project : matchedProjects) {
                if (StringUtils.isNotBlank(project.getCustomerProject()) &&
                        StringUtils.isNotBlank(project.getProjectName())) {
                    tag.addMatchedProject(project.getCustomerProject(), project.getProjectName());
                    
                    if (!matchedInfo.isEmpty()) {
                        matchedInfo.append(", ");
                    }
                    matchedInfo.append(project.getProjectName());
                }
            }
            
            log.info("创建物业服务偏好标签，匹配项目: {}", matchedInfo);
            addTag(tag);
        } else {
            log.info("不满足物业服务偏好标签条件，不添加标签");
        }
    }

    /**
     * 分析兑现承诺标签
     * 标签规则：namelist中超过一半的好评提及兑现承诺
     */
    public void analyzePromiseFulfillmentTag() {
        if (projects.isEmpty()) {
            return;
        }

        List<ProjectDetail> matchedProjects = new ArrayList<>();
        int projectsWithGoodComments = 0;

        for (ProjectDetail project : projects) {
            if (StringUtils.isNotBlank(project.getTextGood())) {
                projectsWithGoodComments++;
                String goodComments = project.getTextGood().toLowerCase();

                // 检查是否提及兑现承诺
                if (goodComments.contains("承诺") ||
                        goodComments.contains("兑现") ||
                        goodComments.contains("信用") ||
                        goodComments.contains("信誉") ||
                        goodComments.contains("言而有信") ||
                        goodComments.contains("诚信") ||
                        goodComments.contains("售后") ||
                        goodComments.contains("保障") ||
                        goodComments.contains("契约")) {
                    matchedProjects.add(project);
                }
            }
        }

        if (projectsWithGoodComments > 0 &&
                (double) matchedProjects.size() / projectsWithGoodComments >= PREFERRED_CATEGORY_THRESHOLD &&
                matchedProjects.size() >= 2) {

            UserProfileTag tag = UserProfileTag.create(
                    TAG_PROMISE_FULFILLMENT,
                    "兑现承诺偏好",
                    "客户购房决策中较为看重开发商对承诺的兑现程度",
                    TAG_TYPE_PURCHASE_DECISION
            );

            // 添加匹配项目
            for (ProjectDetail project : matchedProjects) {
                if (StringUtils.isNotBlank(project.getCustomerProject()) &&
                        StringUtils.isNotBlank(project.getProjectName())) {
                    tag.addMatchedProject(project.getCustomerProject(), project.getProjectName());
                }
            }

            addTag(tag);
        }
    }

    /**
     * 分析高标准偏好标签
     * 标签规则：
     * 1. dataset中>2条数据（不含2条）
     * 2. 但所有数据中的ra1a5仅有1-3分评价，没有其他分值
     *
     * 测试数据：
     */
    public void analyzeHighStandardPreferenceTag() {
        if (projects.isEmpty()) {
            return;
        }

        List<NormalizeData> allRawData = new ArrayList<>();

        // 汇总所有项目的rawData
        for (ProjectDetail project : projects) {
            if (project.getRawData() != null && !project.getRawData().isEmpty()) {
                allRawData.addAll(project.getRawData());
            }
        }

        log.info("高标准偏好标签分析：汇总所有项目的rawData，共{}条记录", allRawData.size());

        // 统计ra1a5字段情况
        int totalRa1a5Count = 0;         // ra1a5有值的记录数
        int lowScoreRa1a5Count = 0;      // ra1a5为1-3分的记录数
        int otherScoreRa1a5Count = 0;    // ra1a5为其他分值的记录数
        
        List<ProjectDetail> matchedProjects = new ArrayList<>();

        for (NormalizeData data : allRawData) {
            Map<String, Object> dataItem = data.getData();
            if (null == dataItem || dataItem.isEmpty()) continue;

            // 获取ra1a5字段值
            Object ratingValue = dataItem.get("ra1a5");
            
            if (ratingValue != null) {
                totalRa1a5Count++;
                
                // 尝试将评分转换为数值并检查是否为1-3分
                try {
                    int score = Integer.parseInt(ratingValue.toString());
                    if (score >= 1 && score <= 3) {
                        lowScoreRa1a5Count++;
                        
                        // 找到对应项目并添加到匹配列表
                        String customerProject = (String)dataItem.getOrDefault("customer_project", "");
                        if (StringUtils.isNotBlank(customerProject)) {
                            for (ProjectDetail project : projects) {
                                if (customerProject.equals(project.getCustomerProject()) && 
                                        !matchedProjects.contains(project)) {
                                    matchedProjects.add(project);
                                }
                            }
                        }
                    } else {
                        // 记录非1-3分的评分
                        otherScoreRa1a5Count++;
                    }
                } catch (NumberFormatException e) {
                    // 忽略非数字值
                    log.debug("rawData中ra1a5字段值[{}]无法转换为整数", ratingValue);
                }
            }
        }

        log.info("高标准偏好标签分析：ra1a5有值记录数={}, 1-3分记录数={}, 其他分值记录数={}", 
                totalRa1a5Count, lowScoreRa1a5Count, otherScoreRa1a5Count);
        
        // 判断是否满足标签条件：
        // 1. 数据集中超过2条数据
        // 2. 所有ra1a5评分都在1-3分，没有其他分值
        if (totalRa1a5Count > 2 && lowScoreRa1a5Count > 0 && otherScoreRa1a5Count == 0) {
            log.info("满足高标准偏好标签条件：共{}条评分数据，全部为1-3分", totalRa1a5Count);
            
            UserProfileTag tag = UserProfileTag.create(
                    TAG_HIGH_STANDARD,
                    "高标准偏好",
                    "客户对居住环境要求较高，倾向选择高标准开发产品",
                    TAG_TYPE_LIVING_PREFERENCE
            );

            // 添加匹配项目
            StringBuilder matchedInfo = new StringBuilder();
            for (ProjectDetail project : matchedProjects) {
                if (StringUtils.isNotBlank(project.getCustomerProject()) &&
                        StringUtils.isNotBlank(project.getProjectName())) {
                    tag.addMatchedProject(project.getCustomerProject(), project.getProjectName());
                    
                    if (!matchedInfo.isEmpty()) {
                        matchedInfo.append(", ");
                    }
                    matchedInfo.append(project.getProjectName());
                }
            }

            log.info("创建高标准偏好标签，匹配项目: {}", matchedInfo);
            addTag(tag);
        } else {
            log.info("不满足高标准偏好标签条件：总记录数={}, 1-3分记录数={}, 其他分值记录数={}", 
                    totalRa1a5Count, lowScoreRa1a5Count, otherScoreRa1a5Count);
        }
    }
} 
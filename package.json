{"type": "module", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "scripts": {"dev": "vite", "build:example": "vue-tsc -b && vite build --mode example", "serve:example": "http-server ./dist-example -o", "svgo": "svgo -f src/assets/icons", "new": "plop", "generate:icons": "esno ./scripts/generate.icons.ts", "lint": "npm-run-all -s lint:tsc lint:eslint lint:stylelint", "lint:tsc": "vue-tsc -b", "lint:eslint": "eslint . --cache --fix", "lint:stylelint": "stylelint \"src/**/*.{css,scss,vue}\" --cache --fix", "preinstall": "npx only-allow pnpm", "postinstall": "simple-git-hooks", "taze": "taze minor -wIr"}, "dependencies": {"@antv/g2plot": "^2.4.33", "@bytemd/plugin-gfm": "^1.22.0", "@bytemd/vue-next": "^1.22.0", "@number-flow/vue": "^0.4.7", "@tinymce/tinymce-vue": "^6.1.0", "@vee-validate/zod": "^4.15.0", "@visactor/vchart": "^1.13.9", "@vueuse/components": "^13.1.0", "@vueuse/core": "^13.1.0", "@vueuse/integrations": "^13.1.0", "animate.css": "^4.1.1", "axios": "^1.9.0", "bytemd": "^1.22.0", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cropperjs": "^1.6.2", "dayjs": "^1.11.13", "defu": "^6.1.4", "disable-devtool": "^0.3.8", "echarts": "^5.6.0", "element-plus": "^2.9.9", "embla-carousel-auto-scroll": "^8.6.0", "embla-carousel-autoplay": "^8.6.0", "embla-carousel-fade": "^8.6.0", "embla-carousel-vue": "^8.6.0", "eruda": "^3.4.1", "es-toolkit": "^1.36.0", "hotkeys-js": "^3.13.9", "lucide-vue-next": "^0.503.0", "medium-zoom": "^1.1.0", "mitt": "^3.0.1", "motion-v": "0.11.0-beta.6", "nprogress": "^0.2.0", "path-browserify": "^1.0.1", "path-to-regexp": "^8.2.0", "pinia": "^3.0.2", "pinyin-pro": "^3.26.0", "print-js": "^1.6.0", "qrcode": "^1.5.4", "qs": "^6.14.0", "reka-ui": "^2.2.0", "scule": "^1.3.0", "sortablejs": "^1.15.6", "spinkit": "^2.0.1", "splitpanes": "^4.0.3", "swiper": "^11.2.6", "tailwind-merge": "^3.2.0", "tinymce": "^7.8.0", "ua-parser-js": "^2.0.3", "v-wave": "^3.0.2", "vconsole": "^3.15.1", "vee-validate": "^4.15.0", "vue": "^3.5.13", "vue-currency-input": "^3.2.1", "vue-esign": "^1.1.4", "vue-hooks-plus": "^2.3.1", "vue-i18n": "^11.1.3", "vue-router": "^4.5.1", "vue-sonner": "^1.3.2", "vxe-table": "^4.13.16", "watermark-js-plus": "^1.6.0", "xe-utils": "^3.7.4", "zod": "^3.24.3"}, "devDependencies": {"@antfu/eslint-config": "^4.12.0", "@faker-js/faker": "^9.7.0", "@iconify/json": "^2.2.333", "@iconify/vue": "^4.3.0", "@intlify/eslint-plugin-vue-i18n": "^4.0.1", "@intlify/unplugin-vue-i18n": "^6.0.8", "@stylistic/stylelint-config": "^2.0.0", "@types/canvas-confetti": "^1.9.0", "@types/nprogress": "^0.2.3", "@types/path-browserify": "^1.0.3", "@types/qrcode": "^1.5.5", "@types/qs": "^6.9.18", "@types/sortablejs": "^1.15.8", "@types/splitpanes": "^2.2.6", "@unocss/eslint-plugin": "^66.0.0", "@unocss/preset-legacy-compat": "^66.0.0", "@vitejs/plugin-legacy": "^6.1.1", "@vitejs/plugin-vue": "^5.2.3", "@vitejs/plugin-vue-jsx": "^4.1.2", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.21", "boxen": "^8.0.1", "eslint": "^9.25.1", "esno": "^4.8.0", "fs-extra": "^11.3.0", "http-server": "^14.1.1", "inquirer": "^12.6.0", "lint-staged": "^15.5.1", "npm-run-all2": "^7.0.2", "picocolors": "^1.1.1", "plop": "^4.0.1", "postcss": "^8.5.3", "postcss-nested": "^7.0.2", "sass-embedded": "^1.87.0", "simple-git-hooks": "^2.13.0", "stylelint": "^16.19.1", "stylelint-config-recess-order": "^6.0.0", "stylelint-config-standard-scss": "^14.0.0", "stylelint-config-standard-vue": "^1.0.0", "stylelint-scss": "^6.11.1", "svgo": "^3.3.2", "taze": "^19.0.4", "typescript": "^5.8.3", "unocss": "^66.0.0", "unocss-preset-animations": "^1.2.1", "unplugin-auto-import": "^19.1.2", "unplugin-turbo-console": "^2.1.3", "unplugin-vue-components": "^28.5.0", "vite": "^6.2.7", "vite-plugin-app-loading": "^0.3.1", "vite-plugin-archiver": "^0.1.2", "vite-plugin-banner": "^0.8.1", "vite-plugin-compression2": "^1.3.3", "vite-plugin-env-parse": "^1.0.15", "vite-plugin-fake-server": "^2.2.0", "vite-plugin-pages": "^0.33.0", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue-devtools": "^7.7.6", "vite-plugin-vue-meta-layouts": "^0.5.1", "vue-tsc": "^2.2.10"}, "simple-git-hooks": {"pre-commit": "pnpm lint-staged", "preserveUnused": true}}
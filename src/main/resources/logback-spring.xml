<?xml version="1.0" encoding="UTF-8"?>
<configuration>

    <contextName>[DipsPro]</contextName>

    <!-- 引入 Spring Boot 默认配置 -->
    <include resource="org/springframework/boot/logging/logback/defaults.xml" />

    <!-- 定义日志文件基础路径变量，优先使用环境变量 LOG_PATH，否则默认为 logs -->
    <property name="LOG_PATH" value="${LOG_PATH:-logs}" />
    <property name="log.charset" value="utf-8" />
    <property name="log.pattern"
        value="%boldMagenta(%contextName) %d{yyyy-MM-dd HH:mm:ss} %green([%thread]) %highlight(%-5level) %boldMagenta(%logger{36}[%M][%L]) - %msg%n" />


    <!-- 控制台输出 Appender -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${log.pattern}</pattern>
            <charset>${log.charset}</charset>
        </encoder>
    </appender>

    <!-- 文件输出 Appender (滚动) -->
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 日志文件路径 -->
        <file>${LOG_PATH}/dips-pro.log</file>
        <!-- 滚动策略，按天和大小滚动 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- 归档文件的命名格式 -->
            <fileNamePattern>${LOG_PATH}/dips-pro-%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <!-- 每个日志文件的最大大小 -->
            <maxFileSize>100MB</maxFileSize>
            <!-- 日志文件保留天数 -->
            <maxHistory>30</maxHistory>
            <!-- 总日志文件大小 -->
            <totalSizeCap>10GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>${log.pattern}</pattern>
            <charset>${log.charset}</charset>
        </encoder>
    </appender>

    <!-- 开发环境配置 -->
    <springProfile name="dev">
        <root level="INFO">
            <appender-ref ref="CONSOLE" />
        </root>
        <!-- 指定包的日志级别 -->
        <logger name="com.dipspro" level="INFO" additivity="false">
            <appender-ref ref="CONSOLE" />
        </logger>
    </springProfile>

    <!-- 测试环境配置 -->
    <springProfile name="test">
        <root level="INFO">
            <appender-ref ref="CONSOLE" />
        </root>
    </springProfile>

    <!-- 生产环境配置 -->
    <springProfile name="prod">
        <root level="INFO">
            <!-- 在生产环境中通常只输出到文件 -->
            <appender-ref ref="FILE" />
        </root>
        <!-- 如果需要，可以为特定包配置不同的级别或Appender -->
        <!-- <logger name="com.dipspro" level="WARN" additivity="false">
              <appender-ref ref="FILE"/>
         </logger> -->
    </springProfile>

    <!-- 如果没有指定 profile，则默认使用控制台输出 -->
    <root level="INFO">
        <appender-ref ref="CONSOLE" />
    </root>

</configuration>
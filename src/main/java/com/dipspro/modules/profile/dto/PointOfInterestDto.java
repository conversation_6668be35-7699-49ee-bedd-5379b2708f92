package com.dipspro.modules.profile.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 兴趣点数据传输对象
 * 
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PointOfInterestDto {
    
    /**
     * 兴趣点ID
     */
    private Long id;

    private String amapId;

    /**
     * 兴趣点名称
     */
    private String name;
    
    /**
     * 兴趣点类型
     */
    private String type;
    private String typeCode;
    private String bizType;

    /**
     * 简化后的类型（用于分组显示）
     */
    private String simpleType;
    
    /**
     * 详细地址
     */
    private String address;
    private String location;

    /**
     * 经度
     */
    private Double lng;
    
    /**
     * 纬度
     */
    private Double lat;
    
    /**
     * 距离项目的距离（米）
     */
    private Double distance;
    private String tel;
    private String cityName;
    private String adName;
    private String tag;

    /**
     * 额外信息（如电话、评分等）
     */
    private String extraInfo;
    
    /**
     * 获取POI类型的简化版本（只取大类）
     * @return 大类名称
     */
    public String getSimpleType() {
        if (type == null || type.isEmpty()) {
            return "";
        }
        
        // 类型格式为 "大类;中类;小类"，取第一部分作为简化版本
        int firstSemicolon = type.indexOf(';');
        if (firstSemicolon > 0) {
            return type.substring(0, firstSemicolon);
        }
        
        return type;
    }
} 
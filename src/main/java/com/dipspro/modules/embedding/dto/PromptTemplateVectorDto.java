package com.dipspro.modules.embedding.dto;

import java.time.Instant;
import java.util.UUID;

import jakarta.persistence.Transient;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 提示词模板向量 DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PromptTemplateVectorDto {

    private Long _id;

    private UUID id;

    @NotNull(message = "模板ID不能为空")
    private UUID templateId;

    @Size(max = 500, message = "典型描述长度不能超过500个字符")
    private String typicalDescription;

    private float[] embedding;

    private Instant createdAt;

    private Instant updatedAt;

    @Size(max = 64, message = "创建者ID长度不能超过64个字符")
    private String createdBy;

    @Size(max = 64, message = "更新者ID长度不能超过64个字符")
    private String updatedBy;

    @Transient
    private double similarity;

    /**
     * 获取向量维度
     */
    public int getDimension() {
        return embedding != null ? embedding.length : 0;
    }
}
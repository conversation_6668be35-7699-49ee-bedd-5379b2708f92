package com.dipspro.modules.embedding.repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.dipspro.modules.embedding.entity.PromptTemplateVector;

@Repository
public interface PromptTemplateVectorRepository extends JpaRepository<PromptTemplateVector, UUID> {

    /**
     * 根据模板ID查找向量
     */
    List<PromptTemplateVector> findByTemplateId(UUID templateId);

    /**
     * 根据模板ID列表批量查找向量
     */
    List<PromptTemplateVector> findByTemplateIdIn(List<UUID> templateIds);

    /**
     * 获取所有向量，按创建时间倒序排列
     */
    List<PromptTemplateVector> findAllByOrderByCreatedAtDesc();

    /**
     * 根据创建者查找向量
     */
    List<PromptTemplateVector> findByCreatedByOrderByCreatedAtDesc(String createdBy);

    /**
     * 检查模板ID是否已存在向量
     */
    boolean existsByTemplateId(UUID templateId);

    /**
     * 根据模板ID删除向量
     */
    void deleteByTemplateId(UUID templateId);

    /**
     * 批量查询向量
     */
    @Query("SELECT ptv FROM PromptTemplateVector ptv WHERE ptv.id IN :ids")
    List<PromptTemplateVector> findByIdIn(@Param("ids") List<UUID> ids);

    /**
     * 根据典型描述模糊查询
     */
    @Query("SELECT ptv FROM PromptTemplateVector ptv WHERE ptv.typicalDescription LIKE %:description% ORDER BY ptv.createdAt DESC")
    List<PromptTemplateVector> findByTypicalDescriptionContaining(@Param("description") String description);

    /**
     * 获取指定数量的最新向量（用于分页或限制结果）
     */
    @Query(value = "SELECT * FROM prompt_template_vectors ORDER BY created_at DESC LIMIT :limit", nativeQuery = true)
    List<PromptTemplateVector> findTopByOrderByCreatedAtDesc(@Param("limit") int limit);

    /**
     * 根据序列ID范围查询（用于分批处理）
     */
    @Query("SELECT ptv FROM PromptTemplateVector ptv WHERE ptv._id BETWEEN :startId AND :endId ORDER BY ptv._id")
    List<PromptTemplateVector> findBySerialIdBetween(@Param("startId") Long startId, @Param("endId") Long endId);
}
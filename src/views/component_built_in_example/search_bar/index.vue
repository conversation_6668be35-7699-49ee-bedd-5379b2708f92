<route lang="yaml">
meta:
  enabled: false
</route>

<script setup lang="ts">
import Demo1 from './_demo1.vue'
import Demo1Raw from './_demo1.vue?raw'
import Demo2 from './_demo2.vue'
import Demo2Raw from './_demo2.vue?raw'
import Demo3 from './_demo3.vue'
import Demo3Raw from './_demo3.vue?raw'
import Demo4 from './_demo4.vue'
import Demo4Raw from './_demo4.vue?raw'
import Demo5 from './_demo5.vue'
import Demo5Raw from './_demo5.vue?raw'
import Demo6 from './_demo6.vue'
import Demo6Raw from './_demo6.vue?raw'
</script>

<template>
  <div>
    <FaPageHeader title="搜索面板" description="FaSearchBar" />
    <FaCodePreview :code="Demo1Raw">
      <Demo1 />
    </FaCodePreview>
    <FaCodePreview title="默认展开" :code="Demo2Raw">
      <Demo2 />
    </FaCodePreview>
    <FaCodePreview title="显示背景" :code="Demo3Raw">
      <Demo3 />
    </FaCodePreview>
    <FaCodePreview title="切换事件" :code="Demo4Raw">
      <Demo4 />
    </FaCodePreview>
    <FaCodePreview title="自定义切换按钮（搭配自定义 grid 布局）" :code="Demo5Raw">
      <Demo5 />
    </FaCodePreview>
    <FaCodePreview title="自定义切换按钮（搭配 el-row 组件）" :code="Demo6Raw">
      <Demo6 />
    </FaCodePreview>
  </div>
</template>

package com.dipspro.modules.profile.processor;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.dipspro.modules.normalize.dto.ProjectProfileRequest;
import com.dipspro.modules.profile.dao.ProjectAnalyticsDao;
import com.dipspro.modules.profile.dao.ProjectEnrichmentDao;
import com.dipspro.modules.profile.dto.ProjectDetail;
import com.dipspro.modules.profile.dto.ProjectProfileDto;
import com.dipspro.modules.profile.service.ProjectDataQueryService;
import com.dipspro.util.DatePeriodUtils;
import com.dipspro.util.MapUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 楼盘画像处理器
 * 
 * <AUTHOR>
 * @since 2025-01-20
 */
@Slf4j
@Component
public class ProjectProfileProcessor {

    @Autowired
    private ProjectDataQueryService projectDataQueryService;

    @Autowired
    private ProjectAnalyticsDao projectAnalyticsDao;

    @Autowired
    private ProjectEnrichmentDao projectEnrichmentDao;

    /**
     * 处理楼盘查询逻辑
     * 
     * @param projectProfile 楼盘画像DTO
     * @param request        请求参数
     * @return 是否找到唯一匹配的楼盘
     */
    public boolean processProjectQuery(ProjectProfileDto projectProfile, ProjectProfileRequest request) {
        String projectName = projectProfile.getProjectName();

        // 1. 先进行精确查询
        List<Map<String, Object>> exactResults = projectDataQueryService.queryProjectByExactName(projectName);

        if (exactResults.size() == 1) {
            // 精确匹配到一个结果
            projectProfile.setQueryStatus("exact");
            enrichProjectProfileWithDetail(projectProfile, exactResults.get(0));
            return true;
        } else if (exactResults.size() > 1) {
            // 精确匹配到多个结果，特殊处理楼盘名称相同，不同开发商的情况
            String customerNameInRequest = projectProfile.getCustomerName();
            if (StringUtils.isNotBlank(customerNameInRequest)) {
                for (Map<String, Object> exactResult : exactResults) {
                    if (StringUtils.equals(customerNameInRequest, exactResult.get("customer_name").toString())) {
                        // 精确匹配到一个结果
                        projectProfile.setQueryStatus("exact");
                        enrichProjectProfileWithDetail(projectProfile, exactResult);
                        return true;
                    }
                }
            }

            // 没有找到匹配的开发商，返回多个选项
            projectProfile.setQueryStatus("multiple");
            return false;
        } else {
            // 2. 精确查询无结果，进行模糊查询
            List<Map<String, Object>> fuzzyResults = projectDataQueryService.queryProjectByFuzzyName(projectName);

            if (fuzzyResults.size() == 1) {
                // 模糊匹配到一个结果
                projectProfile.setQueryStatus("fuzzy");
                enrichProjectProfileWithDetail(projectProfile, fuzzyResults.get(0));
                return true;
            } else if (fuzzyResults.size() > 1) {
                // 模糊匹配到多个结果
                projectProfile.setQueryStatus("multiple");
                return false;
            } else {
                // 未找到任何结果
                projectProfile.setQueryStatus("not_found");
                return false;
            }
        }
    }

    /**
     * 丰富楼盘画像基础数据
     * 
     * @param projectProfile 楼盘画像DTO
     * @param projectData    楼盘数据
     */
    public void enrichProjectProfileWithDetail(ProjectProfileDto projectProfile, Map<String, Object> projectData) {
        // 创建项目详情对象
        ProjectDetail projectDetail = new ProjectDetail();
        fillProjectWithMatchedRecord(projectDetail, projectData);
        projectProfile.setProjectDetail(projectDetail);
    }

    /**
     * 丰富楼盘画像完整数据
     * 
     * @param projectProfile 楼盘画像DTO
     */
    public void enrichProjectProfileWithData(ProjectProfileDto projectProfile) {
        log.info("开始丰富楼盘画像数据");

        // 查询指标得分
        enrichIndexScore(projectProfile);

        // 查询样框、业主阶段
        enrichTotalSample(projectProfile);

        // 查询楼盘地址和开发商
        enrichLjLouPan(projectProfile);
    }

    /**
     * 丰富销售信息相关数据
     * 
     * @param projectProfile 楼盘画像DTO
     */
    public void enrichSalesInfo(ProjectProfileDto projectProfile) {
        // 查询楼盘
        enrichLjLouPan(projectProfile);
        // 查询楼盘户型
        enrichLjLouPanLayout(projectProfile);
        // 查询准业主样本量
        enrichTotalSample(projectProfile);
        // 查询准业主指标得分
        enrichIndexScore(projectProfile, "准业主");
        // 查询年龄段
        enrichAgeGroup(projectProfile, "准业主");
    }

    /**
     * 丰富分析数据
     * 
     * @param projectProfile 楼盘画像DTO
     */
    public void enrichAnalysisData(ProjectProfileDto projectProfile) {
        Map<String, Object> slots = projectProfile.getSlots();

        Object projectNameObj = slots.get("project_name");
        if (null == projectNameObj)
            return;
        String projectName = projectNameObj.toString();

        Object dataPeriodObj = slots.get("data_period");
        if (null == dataPeriodObj)
            return;
        String dataPeriod = dataPeriodObj.toString();

        Object attributeObj = slots.get("attribute");
        if (null == attributeObj)
            return;
        String attribute = attributeObj.toString();

        Object indexNameObj = slots.get("index_name");
        if (null == indexNameObj)
            return;
        String indexName = indexNameObj.toString();

        // 分期格式转换
        dataPeriod = transformDataPeriod(dataPeriod);

        Map<String, Integer> data = projectAnalyticsDao.queryAna(projectName, dataPeriod, attribute, indexName);
        projectProfile.getProjectDetail().setIndexScore(data);
    }

    /**
     * 丰富楼盘的指标得分
     */
    private void enrichIndexScore(ProjectProfileDto projectProfile) {
        Map<String, Integer> indexScore = projectAnalyticsDao.queryIndexScore(
                projectProfile.getProjectName(), new String[] { "ra1a5", "rt0a5" });
        projectProfile.getProjectDetail().setIndexScore(indexScore);
    }

    /**
     * 丰富楼盘的指标得分（带业主类型）
     */
    private void enrichIndexScore(ProjectProfileDto projectProfile, String ownerType) {
        Map<String, Integer> indexScore = projectAnalyticsDao.queryIndexScoreWithOwnerType(
                projectProfile.getProjectName(), ownerType, new String[] { "ra1a5", "rt0a5" });
        Map<String, Map<String, Integer>> indexScoreOwnerType = new HashMap<>();
        indexScoreOwnerType.put(ownerType, indexScore);
        projectProfile.getProjectDetail().setIndexScoreOwnerType(indexScoreOwnerType);
    }

    /**
     * 丰富楼盘的样框数据
     */
    private void enrichTotalSample(ProjectProfileDto projectProfile) {
        Map<String, Object> map = projectAnalyticsDao.queryTotalSample(projectProfile.getProjectName());
        if (null == map)
            return;

        ProjectDetail projectDetail = projectProfile.getProjectDetail();
        projectDetail.setTotalSampleRawData(map);

        Object totalSampleObj = map.get("totalSample");
        Object ownerStage = map.get("ownerStage");
        if (totalSampleObj != null) {
            Long totalSample = (Long) totalSampleObj;
            projectDetail.setTotalSample(totalSample.intValue());
        }
        if (ownerStage != null) {
            projectDetail.setOwnerStage(ownerStage.toString());
        }
    }

    /**
     * 查询年龄段
     */
    private void enrichAgeGroup(ProjectProfileDto projectProfile, String ownerType) {
        Map<String, Integer> ageGroup = projectAnalyticsDao.queryAgeGroup(
                projectProfile.getProjectName(), ownerType);
        projectProfile.getProjectDetail().setAgeGroup(ageGroup);
    }

    /**
     * 丰富楼盘的链家楼盘数据
     */
    private void enrichLjLouPan(ProjectProfileDto projectProfile) {
        if (null == projectProfile.getProjectDetail()) {
            projectProfile.setProjectDetail(new ProjectDetail());
        }

        String ljCode = projectProfile.getProjectDetail().getLjCode();
        if (StringUtils.isBlank(ljCode)) {
            log.warn("链家唯一码为空，无法查询LjLoupan");
            // 初始化空的链家数据，避免 NullPointerException
            projectProfile.getProjectDetail().setLjLouPan(new HashMap<>());
            return;
        }

        // 查询lj_loupan
        Map<String, Object> ljLouPan = projectEnrichmentDao.queryLjLouPan(ljCode);
        if (ljLouPan == null) {
            ljLouPan = new HashMap<>();
        }
        projectProfile.getProjectDetail().setLjLouPan(ljLouPan);

        // 查询地理位置
        if (ljLouPan.containsKey("address")) {
            String address = ljLouPan.get("address") + "";
            if (StringUtils.isNotBlank(address) && !StringUtils.equalsIgnoreCase(address, "null")) {
                projectProfile.getProjectDetail().setAddress(address);
            }
        }

        // 查询开发商
        if (ljLouPan.containsKey("brand_name")) {
            String brandName = ljLouPan.get("brand_name") + "";
            if (StringUtils.isNotBlank(brandName) && !StringUtils.equalsIgnoreCase(brandName, "null")) {
                projectProfile.getProjectDetail().setCustomerName(brandName);
            }
        }
    }

    /**
     * 丰富楼盘户型数据
     */
    private void enrichLjLouPanLayout(ProjectProfileDto projectProfile) {
        if (null == projectProfile.getProjectDetail()) {
            projectProfile.setProjectDetail(new ProjectDetail());
        }

        String ljCode = projectProfile.getProjectDetail().getLjCode();
        if (StringUtils.isBlank(ljCode)) {
            log.warn("链家唯一码为空，无法查询LjLoupan户型");
            return;
        }

        List<Map<String, Object>> ljLouPanLayout = projectEnrichmentDao.queryLjLouPanLayout(ljCode);
        projectProfile.getProjectDetail().setLjLouPanLayout(ljLouPanLayout);
    }

    /**
     * 将匹配的记录数据填充到项目详情中
     */
    public void fillProjectWithMatchedRecord(ProjectDetail project, Map<String, Object> matchedRecord) {
        // 基本项目信息
        project.setCustomerProject(MapUtils.safeGetString(matchedRecord, "customer_project"));
        project.setCustomerName(MapUtils.safeGetString(matchedRecord, "customer_name"));
        project.setProjectName(MapUtils.safeGetString(matchedRecord, "projectname"));
        project.setBuildingGrade(MapUtils.safeGetString(matchedRecord, "building_grade"));

        // 地理信息
        String cityName = MapUtils.safeGetString(matchedRecord, "cityname");
        project.setCity(cityName);
        // 解析省份（从城市名称中提取）
        if (StringUtils.isNotBlank(cityName)) {
            String province = extractProvinceFromCity(cityName);
            project.setProvince(province);
        }

        project.setDistrict(MapUtils.safeGetString(matchedRecord, "district"));

        // 链家相关信息
        project.setLjCode(MapUtils.safeGetString(matchedRecord, "lj_loupan_code"));
        project.setLjLoupanPriceAverage(MapUtils.safeGetString(matchedRecord, "lj_loupan_price_average"));
        project.setLjXiaoquBuildType(MapUtils.safeGetString(matchedRecord, "lj_xiaoqu_build_type"));
        project.setPropertyCostLevel(MapUtils.safeGetString(matchedRecord, "property_cost_level"));
        project.setLjXiaoquPropertyFeeDesc(MapUtils.safeGetString(matchedRecord, "lj_xiaoqu_property_fee_desc"));

        // 链家匹配状态
        String matchStatus = MapUtils.safeGetString(matchedRecord, "match_status");
        project.setMatchedLianjia(
                StringUtils.isNotBlank(matchStatus) && !"no_match".equals(matchStatus));

        // 周边配套数量
        project.setMallAmount(MapUtils.safeGetInteger(matchedRecord, "mall_amount"));
        project.setSupermarketAmount(MapUtils.safeGetInteger(matchedRecord, "supermarket_amount"));
        project.setHospitalAmount(MapUtils.safeGetInteger(matchedRecord, "hospital_amount"));
        project.setParkAmount(MapUtils.safeGetInteger(matchedRecord, "park_amount"));
        project.setSchoolAmount(MapUtils.safeGetInteger(matchedRecord, "school_amount"));
        project.setSubwayStationAmount(MapUtils.safeGetInteger(matchedRecord, "subway_station_amount"));
        project.setBusStopAmount(MapUtils.safeGetInteger(matchedRecord, "bus_stop_amount"));

        // 设置地理位置坐标(经纬度)
        project.setLng(MapUtils.safeGetDouble(matchedRecord, "lng"));
        project.setLat(MapUtils.safeGetDouble(matchedRecord, "lat"));
    }

    /**
     * 从城市名称中提取省份
     */
    public String extractProvinceFromCity(String cityName) {
        if (StringUtils.isBlank(cityName)) {
            return null;
        }

        // 处理直辖市情况（北京市、上海市、天津市、重庆市）
        if (cityName.startsWith("北京") || cityName.startsWith("上海") ||
                cityName.startsWith("天津") || cityName.startsWith("重庆")) {
            return cityName.contains("-") ? cityName.split("-")[0] : cityName;
        }

        // 处理其他省市情况，通常格式为"省份-城市"或直接是城市名
        if (cityName.contains("-")) {
            return cityName.split("-")[0];
        }

        // 如果没有分隔符，直接返回原值
        return cityName;
    }

    /**
     * 时间周期转换方法
     * <p>
     * 支持的输入格式：
     * - 年度：2025年度、2025年全年 → 2025-0112
     * - 季度：2025年一季度、2025年第一季度 → 2025-0103
     * - 半年度：2025年半年度、2025年上半年 → 2025-0106
     * - 月度范围：2025年1-5月、2025年1月至5月 → 2025-0105
     * - 单月：2025年5月 → 2025-05
     *
     * @param dataPeriod 输入的时间周期字符串
     * @return 转换后的标准格式字符串
     */
    private String transformDataPeriod(String dataPeriod) {
        return DatePeriodUtils.transform(dataPeriod);
    }
}
<route lang="yaml">
meta:
  enabled: false
</route>

<script setup lang="ts">
import useMenuBadgeStore from '@/store/modules/menuBadge'

const menuBadgeStore = useMenuBadgeStore()
</script>

<template>
  <div>
    <FaPageHeader title="点标记" description="搭配 Pinia 可实现动态设置" />
    <FaPageMain>
      <div class="space-y-2">
        <div>当前 badge 值：{{ menuBadgeStore.dot }}</div>
        <FaButton @click="menuBadgeStore.switchDot()">
          切换
        </FaButton>
      </div>
    </FaPageMain>
  </div>
</template>

<route lang="yaml">
meta:
  enabled: false
</route>

<script setup lang="ts">
const input = ref('')

function open(url: string) {
  window.open(url, '_blank')
}
</script>

<template>
  <div>
    <FaPageHeader title="水波纹" description="更多设置请查看文档">
      <ElButton @click="open('https://github.com/justintaddei/v-wave')">
        <template #icon>
          <FaIcon name="i-ep:link" />
        </template>
        访问 v-wave
      </ElButton>
    </FaPageHeader>
    <FaPageMain>
      <ElButton v-wave>
        默认按钮
      </ElButton>
      <ElButton v-wave type="primary">
        主要按钮
      </ElButton>
      <ElButton v-wave type="success">
        成功按钮
      </ElButton>
      <ElButton v-wave type="info">
        信息按钮
      </ElButton>
      <ElButton v-wave type="warning">
        警告按钮
      </ElButton>
      <ElButton v-wave type="danger">
        危险按钮
      </ElButton>
      <div class="my-4">
        <ElImage v-wave src="https://fantastic-admin.hurui.me/logo.svg" class="h-25 w-25" />
      </div>
      <div v-wave class="my-4 h-25 w-25 flex-center border">
        点击我
      </div>
      <div
        v-wave="{
          color: '#f00',
          finalOpacity: 0.3,
        }" class="my-4 h-25 w-25 flex-center border"
      >
        设置颜色
      </div>
      <div class="w-50">
        <ElInput v-model="input" v-wave>
          <template #append>
            <ElButton v-wave-trigger>
              <template #icon>
                <svg-icon name="ep:search" class="cursor-pointer" />
              </template>
            </ElButton>
          </template>
        </ElInput>
      </div>
    </FaPageMain>
  </div>
</template>

<style scoped>
.block {
  border: 1px solid #000;
}
</style>

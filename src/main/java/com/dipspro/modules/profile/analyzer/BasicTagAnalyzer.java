package com.dipspro.modules.profile.analyzer;

import com.dipspro.modules.normalize.dto.NormalizeData;
import com.dipspro.modules.profile.dto.ProjectDetail;
import com.dipspro.modules.profile.dto.UserProfileTag;
import com.dipspro.util.ProjectStatsUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.util.*;

import static com.dipspro.constant.ProjectStatsConstants.*;

/**
 * 基础标签分析器 - 处理多次置业、投资客等基础标签
 *
 * <AUTHOR>
 */
@Slf4j
public class BasicTagAnalyzer extends UserProfileAnalyzer {

    /**
     * 构造函数
     *
     * @param projects        项目列表
     * @param userProfileTags 用户画像标签列表
     */
    public BasicTagAnalyzer(List<ProjectDetail> projects, List<UserProfileTag> userProfileTags) {
        super(projects, userProfileTags);
    }

    @Override
    protected void doAnalyze(StringRedisTemplate redisTemplate) {
        // 实现所有分析方法
        analyzeHasPropertyExperienceTag();
        analyzeMultiplePropertyTag();
        analyzeHighNetWorthTag();
        analyzeInvestorTag();
        analyzeCrossCityPropertyTag();
        analyzeGrowthCustomerTag();
        analyzeTourismRetirementTag();
        analyzeHighStandardTag();
    }

    /**
     * 分析置业经历标签
     * 标签规则：只要有一条记录，就可以打上有置业经历的标签
     */
    public void analyzeHasPropertyExperienceTag() {
        if (!projects.isEmpty()) {
            UserProfileTag tag = new UserProfileTag();
            tag.setCode(TAG_HAS_PROPERTY_EXPERIENCE); // 使用常量 和 Lombok 生成的 setter
            tag.setName("置业经历"); // 使用 Lombok 生成的 setter
            tag.setDescription("客户在历史购买记录中拥有房产");
            tag.setType(TAG_TYPE_PURCHASE_BEHAVIOR); // 使用已有的标签类型

            // 添加所有项目作为匹配项
            for (ProjectDetail project : projects) {
                if (StringUtils.isNotBlank(project.getCustomerProject()) &&
                        StringUtils.isNotBlank(project.getProjectName())) {
                    tag.addMatchedProject(project.getCustomerProject(), project.getProjectName());
                }
            }
            addTag(tag);
        }
    }

    /**
     * 分析多次置业经历标签
     * 标签规则：namelist中能找到>2套房产（不含2套）
     */
    public void analyzeMultiplePropertyTag() {
        if (projects.size() > 2) {
            UserProfileTag tag = UserProfileTag.create(
                    TAG_MULTIPLE_PROPERTY,
                    "多次置业经历",
                    "客户在历史购买记录中拥有2套以上的房产",
                    TAG_TYPE_PURCHASE_BEHAVIOR
            );

            // 添加所有项目作为匹配项
            for (ProjectDetail project : projects) {
                if (StringUtils.isNotBlank(project.getCustomerProject()) &&
                        StringUtils.isNotBlank(project.getProjectName())) {
                    tag.addMatchedProject(project.getCustomerProject(), project.getProjectName());
                }
            }

            addTag(tag);
        }
    }

    /**
     * 分析高净值客户标签
     * 标签规则：【多次置业经历】，且（多套房产均为改善或高端项目 or 高端项目业主 or 物业费8元以上业主）
     */
    public void analyzeHighNetWorthTag() {
        // 先检查是否已有多次置业经历标签
        boolean hasMultiplePropertyTag = hasTag(TAG_MULTIPLE_PROPERTY);

        if (!hasMultiplePropertyTag) {
            return; // 不满足前提条件
        }

        boolean allPremiumProperties = true;
        boolean hasHighEndProperty = false;
        boolean hasHighPropertyFee = false;

        // 因归一数据中大量非标数据结构，打印日志以追踪
        log.info("分析是否高净值客户：");
        StringBuilder gradeBuilder = new StringBuilder("楼盘档次: ");
        StringBuilder propertyFeeBuilder = new StringBuilder("物业费: ");
        for (ProjectDetail project : projects) {
            // 检查是否高端项目
            boolean isPremiumProperty = false;
            String buildingGrade = project.getBuildingGrade();
            if (StringUtils.isNotBlank(buildingGrade)
                    && !StringUtils.equalsIgnoreCase(buildingGrade, "null")) {
                String grade = buildingGrade.toLowerCase();
                gradeBuilder.append(grade).append(",");
                isPremiumProperty = grade.contains("高端") || grade.contains("改善") ||
                        grade.contains("豪宅") || grade.contains("精品");

                if (grade.contains("高端") || grade.contains("豪宅")) {
                    hasHighEndProperty = true;
                }
            }

            // 检查物业费是否高于8元
            if (StringUtils.isNotBlank(project.getLjXiaoquPropertyFeeDesc())) {
                double fee = ProjectStatsUtil.extractPropertyFee(project.getLjXiaoquPropertyFeeDesc());
                propertyFeeBuilder.append(fee).append("元,");
                if (fee >= PROPERTY_FEE_HIGH_END) {
                    hasHighPropertyFee = true;
                }
            }

            allPremiumProperties = allPremiumProperties && isPremiumProperty;
        }
        log.info("楼盘档次: {}", gradeBuilder);
        log.info("物业费: {}", propertyFeeBuilder);

        // 如果满足条件，添加高净值客户标签
        if (allPremiumProperties || hasHighEndProperty || hasHighPropertyFee) {
            UserProfileTag tag = UserProfileTag.create(
                    TAG_HIGH_NET_WORTH,
                    "高净值客户",
                    "客户拥有多套房产，且为高端项目或物业费较高",
                    TAG_TYPE_CUSTOMER_VALUE
            );

            // 添加满足条件的项目作为匹配项
            for (ProjectDetail project : projects) {
                if (StringUtils.isNotBlank(project.getCustomerProject()) &&
                        StringUtils.isNotBlank(project.getProjectName())) {
                    tag.addMatchedProject(project.getCustomerProject(), project.getProjectName());
                }
            }

            addTag(tag);
            log.info("高净值客户标签已添加，{}", tag);
        }
    }

    /**
     * 分析投资客标签
     * 标签规则：同一年namelist准业主持有房产>=2（包含2套） or 同一年namelist同项目四类业主同属性持有房产>=2 or namelist中能找到>4套房产（不含4套）
     */
    public void analyzeInvestorTag() {
        // 检查是否拥有超过4套房产
        if (projects.size() > 4) {
            addInvestorTag();
            return;
        }

        // 按年份分组统计房产数量
        Map<String, Integer> propertiesByYear = new HashMap<>();
        Map<String, Map<String, Integer>> propertiesByYearAndProject = new HashMap<>();

        for (ProjectDetail project : projects) {
            if (StringUtils.isBlank(project.getSignDatetime())) {
                continue;
            }

            // 提取年份
            String year = null;
            if (project.getSignDatetime().length() >= 4) {
                year = project.getSignDatetime().substring(0, 4);
            }

            if (StringUtils.isBlank(year)) {
                continue;
            }

            // 按年份统计
            propertiesByYear.put(year, propertiesByYear.getOrDefault(year, 0) + 1);

            // 按年份和项目统计
            if (!propertiesByYearAndProject.containsKey(year)) {
                propertiesByYearAndProject.put(year, new HashMap<>());
            }

            String projectKey = project.getCustomerProject();
            if (StringUtils.isBlank(projectKey)) {
                projectKey = project.getProjectName();
            }

            if (StringUtils.isNotBlank(projectKey)) {
                Map<String, Integer> projectMap = propertiesByYearAndProject.get(year);
                projectMap.put(projectKey, projectMap.getOrDefault(projectKey, 0) + 1);
            }
        }

        // 检查同一年是否持有2套及以上房产
        for (Map.Entry<String, Integer> entry : propertiesByYear.entrySet()) {
            if (entry.getValue() >= 2) {
                addInvestorTag();
                return;
            }
        }

        // 检查同一年同项目是否持有2套及以上房产
        for (Map<String, Integer> projectMap : propertiesByYearAndProject.values()) {
            for (Integer count : projectMap.values()) {
                if (count >= 2) {
                    addInvestorTag();
                    return;
                }
            }
        }
    }

    /**
     * 添加投资客标签
     */
    private void addInvestorTag() {
        UserProfileTag tag = UserProfileTag.create(
                TAG_INVESTOR,
                "投资客",
                "客户在短期内购买多套房产或总计持有较多房产",
                TAG_TYPE_PURCHASE_BEHAVIOR
        );

        // 添加所有项目作为匹配项
        for (ProjectDetail project : projects) {
            if (StringUtils.isNotBlank(project.getCustomerProject()) &&
                    StringUtils.isNotBlank(project.getProjectName())) {
                tag.addMatchedProject(project.getCustomerProject(), project.getProjectName());
            }
        }

        addTag(tag);
    }

    /**
     * 分析跨城市置业经历标签
     * 标签规则：namelist中能找到>=2套房产（含2套），且在不同城市有置业经历
     */
    public void analyzeCrossCityPropertyTag() {
        if (projects.size() < 2) {
            return;
        }

        // 统计不同城市数量
        Set<String> cities = new HashSet<>();
        for (ProjectDetail project : projects) {
            if (StringUtils.isNotBlank(project.getCity())) {
                cities.add(project.getCity());
            }
        }

        if (cities.size() >= 2) {
            UserProfileTag tag = UserProfileTag.create(
                    TAG_CROSS_CITY_PROPERTY,
                    "跨城市置业经历",
                    "客户在" + String.join("、", cities) + "等多个城市有置业经历",
                    TAG_TYPE_PURCHASE_BEHAVIOR
            );

            // 添加所有项目作为匹配项
            for (ProjectDetail project : projects) {
                if (StringUtils.isNotBlank(project.getCustomerProject()) &&
                        StringUtils.isNotBlank(project.getProjectName())) {
                    tag.addMatchedProject(project.getCustomerProject(), project.getProjectName());
                }
            }

            addTag(tag);
        }
    }

    /**
     * 分析成长型客户标签
     * 标签规则：namelist中能找到>=2套房产（含2套），且最近一次购房项目楼盘档次高于其他房产
     * 取数依据：提取名单数据中 d_owner_type_four = '准业主' 并且 data_period最近一次
     */
    public void analyzeGrowthCustomerTag() {
        if (projects.size() < 2) {
            return;
        }

        // 过滤筛选出符合条件的准业主项目
        List<ProjectDetail> prospectsProjects = new ArrayList<>();

        for (ProjectDetail project : projects) {
            if (project.getNameListData() != null && !project.getNameListData().isEmpty()) {
                // 检查是否满足"准业主"条件
                boolean isProspect = false;
                String latestDataPeriod = null;

                for (NormalizeData data : project.getNameListData()) {
                    Map<String, Object> dataMap = data.getData();
                    if (dataMap != null) {
                        // 检查是否为准业主
                        Object ownerType = dataMap.get("d_owner_type_four");
                        if (ownerType != null && "准业主".equals(ownerType.toString())) {
                            isProspect = true;

                            // 获取data_period字段
                            Object dataPeriod = dataMap.get("data_period");
                            if (dataPeriod != null) {
                                String period = dataPeriod.toString();
                                // 更新最新data_period
                                if (latestDataPeriod == null || period.compareTo(latestDataPeriod) > 0) {
                                    latestDataPeriod = period;
                                }
                            }
                        }
                    }
                }

                // 如果是准业主并且有data_period信息，将其添加到列表
                if (isProspect && latestDataPeriod != null) {
                    project.setSignDatetime(latestDataPeriod); // 使用data_period作为签约时间进行排序
                    prospectsProjects.add(project);
                }
            }
        }

        // 如果筛选后没有满足条件的项目或只有一个项目，则无法进行比较
        if (prospectsProjects.size() < 2) {
            return;
        }

        // 查找签约时间最近的准业主项目（即data_period最新的项目）
        ProjectDetail latestProject = ProjectStatsUtil.findLatestProject(prospectsProjects);

        // 如果没有找到有效的最近项目，则无法分析
        if (latestProject == null) {
            return;
        }

        // 如果没有楼盘档次信息，无法判断
        if (StringUtils.isBlank(latestProject.getBuildingGrade())) {
            return;
        }

        // 获取最新项目的档次权重
        int latestGradeWeight = ProjectStatsUtil.getBuildingGradeWeight(latestProject.getBuildingGrade());

        if (latestGradeWeight == 0) {
            log.info("分析[成长型客户]标签 - 楼盘[{}]没有档次数据[{}]", latestProject.getCustomerProject(), latestProject.getBuildingGrade());
            return; // 无法识别最新项目的档次
        }

        // 检查最新项目是否档次高于之前的项目
        boolean isUpgrade = true;
        for (ProjectDetail project : prospectsProjects) {
            // 跳过最新项目和没有档次信息的项目
            if (project == latestProject || StringUtils.isBlank(project.getBuildingGrade())) {
                continue;
            }

            int gradeWeight = ProjectStatsUtil.getBuildingGradeWeight(project.getBuildingGrade());

            // 如果存在之前的项目档次高于或等于最新项目，则不是升级
            if (gradeWeight >= latestGradeWeight) {
                isUpgrade = false;
                break;
            }
        }

        if (isUpgrade) {
            UserProfileTag tag = UserProfileTag.create(
                    TAG_GROWTH_CUSTOMER,
                    "成长型客户",
                    "客户最近购买的房产档次高于之前购买的房产",
                    TAG_TYPE_PURCHASE_BEHAVIOR
            );

            // 添加所有项目作为匹配项，重点是最新的项目
            tag.addMatchedProject(
                    latestProject.getCustomerProject(),
                    latestProject.getProjectName()
            );

            for (ProjectDetail project : prospectsProjects) {
                if (project != latestProject &&
                        StringUtils.isNotBlank(project.getCustomerProject()) &&
                        StringUtils.isNotBlank(project.getProjectName())) {
                    tag.addMatchedProject(project.getCustomerProject(), project.getProjectName());
                }
            }

            addTag(tag);
        }
    }

    /**
     * 分析文旅养老置业经历标签
     * 标签规则：跨城市置业经历，且在云南/海南/惠州/秦皇岛/贵州/广西持有房产
     */
    public void analyzeTourismRetirementTag() {
        // 先检查是否有跨城市置业标签
        boolean hasCrossCityTag = hasTag(TAG_CROSS_CITY_PROPERTY);

        if (!hasCrossCityTag) {
            return;
        }

        // 获取旅游养老目的地列表
        Set<String> tourismRetirementDestinations = ProjectStatsUtil.getTourismRetirementDestinations();

        // 检查是否在这些地区有房产
        List<ProjectDetail> matchedProjects = new ArrayList<>();

        for (ProjectDetail project : projects) {
            if (StringUtils.isBlank(project.getProvince()) && StringUtils.isBlank(project.getCity())) {
                continue;
            }

            String location = project.getProvince() + project.getCity();
            boolean isMatch = tourismRetirementDestinations.stream()
                    .anyMatch(location::contains);

            if (isMatch) {
                matchedProjects.add(project);
            }
        }

        if (!matchedProjects.isEmpty()) {
            UserProfileTag tag = UserProfileTag.create(
                    TAG_TOURISM_RETIREMENT,
                    "文旅养老置业经历",
                    "客户在文旅养老热门目的地有置业经历",
                    TAG_TYPE_PURCHASE_BEHAVIOR
            );

            // 添加匹配的项目
            for (ProjectDetail project : matchedProjects) {
                if (StringUtils.isNotBlank(project.getCustomerProject()) &&
                        StringUtils.isNotBlank(project.getProjectName())) {
                    tag.addMatchedProject(project.getCustomerProject(), project.getProjectName());
                }
            }

            addTag(tag);
        }
    }

    /**
     * 分析高标准偏好标签
     * 标签规则：dataset中>2条数据（不含2条），但ra1a5仅有1-3分评价
     */
    public void analyzeHighStandardTag() {
        if (projects.size() <= 2) {
            return;
        }

        // 统计评分较低的项目
        int projectsWithLowRating = 0;
        List<ProjectDetail> matchedProjects = new ArrayList<>();

        for (ProjectDetail project : projects) {
            if (StringUtils.isNotBlank(project.getRa1a5Score())) {
                try {
                    double score = Double.parseDouble(project.getRa1a5Score());
                    if (score >= 1.0 && score <= 3.0) {
                        projectsWithLowRating++;
                        matchedProjects.add(project);
                    }
                } catch (NumberFormatException e) {
                    // 忽略解析错误
                }
            }
        }

        // 计算有评分的项目数量
        int projectsWithRating = (int) projects.stream()
                .filter(p -> StringUtils.isNotBlank(p.getRa1a5Score()))
                .count();

        // 如果所有有评分的项目评分都是低分
        if (projectsWithRating > 0 && projectsWithLowRating == projectsWithRating) {
            UserProfileTag tag = UserProfileTag.create(
                    TAG_HIGH_STANDARD,
                    "高标准偏好",
                    "客户对房产有较高标准，不易满足",
                    TAG_TYPE_CUSTOMER_TRAIT
            );

            // 添加满足条件的项目
            for (ProjectDetail project : matchedProjects) {
                if (StringUtils.isNotBlank(project.getCustomerProject()) &&
                        StringUtils.isNotBlank(project.getProjectName())) {
                    tag.addMatchedProject(project.getCustomerProject(), project.getProjectName());
                }
            }

            addTag(tag);
        }
    }

} 
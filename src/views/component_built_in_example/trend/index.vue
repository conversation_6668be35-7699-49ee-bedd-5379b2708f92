<route lang="yaml">
meta:
  enabled: false
</route>

<script setup lang="ts">
import Demo1 from './_demo1.vue'
import Demo1Raw from './_demo1.vue?raw'
import Demo2 from './_demo2.vue'
import Demo2Raw from './_demo2.vue?raw'
import Demo3 from './_demo3.vue'
import Demo3Raw from './_demo3.vue?raw'
</script>

<template>
  <div>
    <FaPageHeader title="趋势符号">
      <template #description>
        <div class="space-y-2">
          <p>FaTrend</p>
          <p>标记上升和下降趋势。通常用绿色代表“好”，红色代表“不好”，股票涨跌场景除外</p>
        </div>
      </template>
    </FaPageHeader>
    <FaCodePreview :code="Demo1Raw">
      <Demo1 />
    </FaCodePreview>
    <FaCodePreview title="颜色反转" :code="Demo2Raw">
      <Demo2 />
    </FaCodePreview>
    <FaCodePreview title="前缀后缀" :code="Demo3Raw">
      <Demo3 />
    </FaCodePreview>
  </div>
</template>

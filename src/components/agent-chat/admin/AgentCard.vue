<template>
  <div class="agent-card" :class="{ disabled: agent.status !== 'ACTIVE' }">
    <!-- 卡片头部 -->
    <div class="card-header">
      <div class="agent-info">
        <div class="agent-avatar">
          <i :class="getCategoryIcon(agent.category)"></i>
        </div>
        <div class="agent-basic">
          <h4 class="agent-name">{{ agent.name }}</h4>
          <div class="agent-meta">
            <span class="category-tag" :class="`category-${agent.category?.toLowerCase()}`">
              {{ getCategoryLabel(agent.category) }}
            </span>
            <span class="status-badge" :class="`status-${agent.status?.toLowerCase()}`">
              {{ getStatusLabel(agent.status) }}
            </span>
          </div>
        </div>
      </div>

      <div class="card-actions">
        <button
          @click="$emit('toggle-status', agent)"
          class="action-btn"
          :class="{ active: agent.status === 'ACTIVE' }"
          :title="agent.status === 'ACTIVE' ? '禁用' : '启用'"
        >
          <i :class="agent.status === 'ACTIVE' ? 'i-carbon-pause' : 'i-carbon-play'"></i>
        </button>

        <button
          @click="showActions = !showActions"
          class="action-btn more-btn"
          :class="{ active: showActions }"
          title="更多操作"
        >
          <i class="i-carbon-overflow-menu-vertical"></i>
        </button>
      </div>
    </div>

    <!-- 卡片内容 -->
    <div class="card-content">
      <p class="agent-description">{{ agent.description }}</p>

      <!-- 能力标签 -->
      <div v-if="agent.capabilities && agent.capabilities.length > 0" class="capabilities">
        <div class="capabilities-header">
          <span class="capabilities-title">能力</span>
          <span class="capabilities-count">{{ agent.capabilities.length }}</span>
        </div>
        <div class="capability-tags">
          <span
            v-for="capability in displayCapabilities"
            :key="capability"
            class="capability-tag"
          >
            {{ getCapabilityLabel(capability) }}
          </span>
          <span
            v-if="agent.capabilities.length > maxDisplayCapabilities"
            class="capability-tag more"
            @click="toggleCapabilities"
          >
            +{{ agent.capabilities.length - maxDisplayCapabilities }}
          </span>
        </div>
      </div>

      <!-- 配置信息 -->
      <div class="config-info">
        <div class="config-item">
          <span class="config-label">优先级</span>
          <div class="priority-bar">
            <div
              class="priority-fill"
              :style="{ width: `${agent.priority || 50}%` }"
            ></div>
            <span class="priority-value">{{ agent.priority || 50 }}</span>
          </div>
        </div>

        <div class="config-item">
          <span class="config-label">超时</span>
          <span class="config-value">{{ agent.timeout || 30 }}s</span>
        </div>

        <div class="config-item">
          <span class="config-label">重试</span>
          <span class="config-value">{{ agent.maxRetries || 3 }}次</span>
        </div>
      </div>
    </div>

    <!-- 卡片底部 -->
    <div class="card-footer">
      <div class="timestamp">
        <i class="i-carbon-time"></i>
        <span>{{ formatTime(agent.updatedAt || agent.createdAt) }}</span>
      </div>

      <div class="footer-actions">
        <button @click="$emit('edit', agent)" class="footer-btn">
          <i class="i-carbon-edit"></i>
          编辑
        </button>
        <button @click="testAgent" class="footer-btn" :disabled="testing">
          <i class="i-carbon-play-filled"></i>
          {{ testing ? '测试中...' : '测试' }}
        </button>
      </div>
    </div>

    <!-- 下拉菜单 -->
    <div v-if="showActions" class="actions-menu" ref="actionsMenu">
      <button @click="$emit('edit', agent)" class="menu-item">
        <i class="i-carbon-edit"></i>
        <span>编辑</span>
      </button>
      <button @click="$emit('duplicate', agent)" class="menu-item">
        <i class="i-carbon-copy"></i>
        <span>复制</span>
      </button>
      <button @click="exportAgent" class="menu-item">
        <i class="i-carbon-download"></i>
        <span>导出</span>
      </button>
      <div class="menu-divider"></div>
      <button @click="$emit('delete', agent)" class="menu-item danger">
        <i class="i-carbon-trash-can"></i>
        <span>删除</span>
      </button>
    </div>
  </div>

  <!-- 测试结果对话框 -->
  <TestResultDialog
    v-model:visible="testDialogVisible"
    :status="testStatus"
    :result="testResult"
    :error-message="testErrorMessage"
    :error-details="testErrorDetails"
    :test-input="testInput"
    :on-retry="retryTest"
    @cancel="cancelTest"
    @retry="retryTest"
  />
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import type { AgentDefinition } from '@/api/agentManagement'
import { AgentManagementService } from '@/api/agentManagement'
import TestResultDialog from './TestResultDialog.vue'

interface Props {
  agent: AgentDefinition
}

const props = defineProps<Props>()

const emit = defineEmits<{
  edit: [agent: AgentDefinition]
  duplicate: [agent: AgentDefinition]
  delete: [agent: AgentDefinition]
  'toggle-status': [agent: AgentDefinition]
  'test-start': [agentId: number]
  'test-end': [agentId: number]
}>()

// 响应式数据
const showActions = ref(false)
const showAllCapabilities = ref(false)
const testing = ref(false)
const actionsMenu = ref<HTMLElement>()
const maxDisplayCapabilities = 3

// 测试相关状态
const testDialogVisible = ref(false)
const testStatus = ref<'testing' | 'success' | 'error'>('testing')
const testResult = ref<any>(null)
const testErrorMessage = ref('')
const testErrorDetails = ref('')
const testInput = ref('')

// 计算属性
const displayCapabilities = computed(() => {
  if (!props.agent.capabilities) return []

  if (showAllCapabilities.value || props.agent.capabilities.length <= maxDisplayCapabilities) {
    return props.agent.capabilities
  }

  return props.agent.capabilities.slice(0, maxDisplayCapabilities)
})

// 生命周期
onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})

// 方法
function getCategoryIcon(category?: string): string {
  const icons = {
    ANALYSIS: 'i-carbon-analytics',
    PROCESSING: 'i-carbon-data-processing',
    INFERENCE: 'i-carbon-machine-learning',
    REPORTING: 'i-carbon-document',
    VISUALIZATION: 'i-carbon-chart-line'
  }
  return icons[category as keyof typeof icons] || 'i-carbon-robot'
}

function getCategoryLabel(category?: string): string {
  const labels = {
    ANALYSIS: '数据分析',
    PROCESSING: '数据处理',
    INFERENCE: '推理',
    REPORTING: '报告生成',
    VISUALIZATION: '可视化'
  }
  return labels[category as keyof typeof labels] || category || '未分类'
}

function getStatusLabel(status?: string): string {
  const labels = {
    ACTIVE: '启用',
    INACTIVE: '禁用',
    DRAFT: '草稿'
  }
  return labels[status as keyof typeof labels] || status || '未知'
}

function getCapabilityLabel(capability: string): string {
  const labels = {
    data_analysis: '数据分析',
    chart_generation: '图表生成',
    report_writing: '报告撰写',
    data_processing: '数据处理',
    pattern_recognition: '模式识别',
    prediction: '预测分析'
  }
  return labels[capability as keyof typeof labels] || capability
}

function formatTime(timeString?: string): string {
  if (!timeString) return '未知'

  const time = new Date(timeString)
  const now = new Date()
  const diff = now.getTime() - time.getTime()

  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))

  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  if (days < 7) return `${days}天前`

  return time.toLocaleDateString('zh-CN', {
    month: 'short',
    day: 'numeric'
  })
}

function toggleCapabilities() {
  showAllCapabilities.value = !showAllCapabilities.value
}

async function testAgent() {
  if (!props.agent.id) {
    showTestError('无法测试', 'Agent ID 不存在')
    return
  }

  // 开始测试
  startTest()

  try {
    const testData = {
      input: '这是一个测试消息，用于验证Agent的响应能力',
      parameters: {
        systemPrompt: props.agent.systemPrompt,
        configuration: props.agent.configuration || {}
      }
    }

    // 保存测试输入用于显示
    testInput.value = testData.input

    const response = await AgentManagementService.testAgent(
      props.agent.id,
      testData
    )

    if (response.success) {
      showTestSuccess(response.data)
    } else {
      throw new Error(response.message || response.error || '测试失败')
    }
  } catch (error: any) {
    console.error('Agent测试失败:', error)
    showTestError(error.message || '网络连接错误', error.stack || '')
  } finally {
    testing.value = false

    // 使用nextTick避免递归更新
    nextTick(() => {
      if (props.agent.id) {
        emit('test-end', props.agent.id)
      }
    })
  }
}

// 测试相关辅助方法
function startTest() {
  testing.value = true
  testStatus.value = 'testing'
  testResult.value = null
  testErrorMessage.value = ''
  testErrorDetails.value = ''
  testDialogVisible.value = true
  showActions.value = false // 关闭操作菜单

  // 使用nextTick避免递归更新
  nextTick(() => {
    if (props.agent.id) {
      emit('test-start', props.agent.id)
    }
  })
}

function showTestSuccess(result: any) {
  testStatus.value = 'success'
  testResult.value = result
}

function showTestError(message: string, details?: string) {
  testStatus.value = 'error'
  testErrorMessage.value = message
  testErrorDetails.value = details || ''
  testDialogVisible.value = true
}

function cancelTest() {
  testing.value = false
  testDialogVisible.value = false

  // 使用nextTick避免递归更新
  nextTick(() => {
    if (props.agent.id) {
      emit('test-end', props.agent.id)
    }
  })
}

function retryTest() {
  testDialogVisible.value = false
  // 延迟一下再重新测试，让对话框完全关闭
  setTimeout(() => {
    testAgent()
  }, 100)
}

function exportAgent() {
  const data = JSON.stringify(props.agent, null, 2)
  const blob = new Blob([data], { type: 'application/json' })
  const url = URL.createObjectURL(blob)

  const a = document.createElement('a')
  a.href = url
  a.download = `agent-${props.agent.name}.json`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)

  URL.revokeObjectURL(url)
  showActions.value = false
}

function handleClickOutside(event: Event) {
  if (showActions.value &&
      actionsMenu.value &&
      !actionsMenu.value.contains(event.target as Node)) {
    showActions.value = false
  }
}
</script>

<style scoped>
.agent-card {
  @apply relative bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm hover:shadow-md transition-all duration-200;
}

.agent-card.disabled {
  @apply opacity-60;
}

.card-header {
  @apply flex items-start justify-between p-4 pb-3;
}

.agent-info {
  @apply flex gap-3 flex-1;
}

.agent-avatar {
  @apply w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center text-blue-600 dark:text-blue-400 text-xl flex-shrink-0;
}

.agent-basic {
  @apply flex-1 min-w-0;
}

.agent-name {
  @apply text-lg font-semibold text-gray-900 dark:text-white m-0 mb-1 truncate;
}

.agent-meta {
  @apply flex gap-2 flex-wrap;
}

.category-tag {
  @apply inline-flex items-center px-2 py-1 text-xs font-medium rounded-full;
}

.category-analysis {
  @apply bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300;
}

.category-processing {
  @apply bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300;
}

.category-inference {
  @apply bg-purple-100 dark:bg-purple-900 text-purple-700 dark:text-purple-300;
}

.category-reporting {
  @apply bg-orange-100 dark:bg-orange-900 text-orange-700 dark:text-orange-300;
}

.category-visualization {
  @apply bg-pink-100 dark:bg-pink-900 text-pink-700 dark:text-pink-300;
}

.status-badge {
  @apply inline-flex items-center px-2 py-1 text-xs font-medium rounded-full;
}

.status-active {
  @apply bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300;
}

.status-inactive {
  @apply bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300;
}

.status-draft {
  @apply bg-yellow-100 dark:bg-yellow-900 text-yellow-700 dark:text-yellow-300;
}

.card-actions {
  @apply flex gap-1;
}

.action-btn {
  @apply w-8 h-8 rounded-full bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 flex items-center justify-center text-gray-600 dark:text-gray-400 transition-colors;
}

.action-btn.active {
  @apply bg-blue-500 text-white;
}

.more-btn.active {
  @apply bg-gray-200 dark:bg-gray-600;
}

.card-content {
  @apply px-4 pb-3 space-y-3;
}

.agent-description {
  @apply text-sm text-gray-600 dark:text-gray-400 m-0 line-clamp-2;
}

.capabilities {
  @apply space-y-2;
}

.capabilities-header {
  @apply flex items-center justify-between;
}

.capabilities-title {
  @apply text-xs font-medium text-gray-700 dark:text-gray-300;
}

.capabilities-count {
  @apply text-xs text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 px-1.5 py-0.5 rounded-full;
}

.capability-tags {
  @apply flex flex-wrap gap-1;
}

.capability-tag {
  @apply inline-flex items-center px-2 py-1 text-xs bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded;
}

.capability-tag.more {
  @apply cursor-pointer hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors;
}

.config-info {
  @apply grid grid-cols-3 gap-3 text-xs;
}

.config-item {
  @apply space-y-1;
}

.config-label {
  @apply block text-gray-500 dark:text-gray-400 font-medium;
}

.config-value {
  @apply text-gray-900 dark:text-white font-medium;
}

.priority-bar {
  @apply relative w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden;
}

.priority-fill {
  @apply h-full bg-blue-500 transition-all duration-300;
}

.priority-value {
  @apply absolute inset-0 flex items-center justify-center text-xs text-gray-700 dark:text-gray-300 font-medium;
}

.card-footer {
  @apply flex items-center justify-between px-4 py-3 border-t border-gray-100 dark:border-gray-700 bg-gray-50 dark:bg-gray-900;
}

.timestamp {
  @apply flex items-center gap-1 text-xs text-gray-500 dark:text-gray-400;
}

.footer-actions {
  @apply flex gap-2;
}

.footer-btn {
  @apply flex items-center gap-1 px-3 py-1.5 text-xs bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors;
}

.footer-btn:disabled {
  @apply opacity-50 cursor-not-allowed;
}

.actions-menu {
  @apply absolute top-16 right-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg py-1 z-50 min-w-[120px];
}

.menu-item {
  @apply w-full flex items-center gap-2 px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-left;
}

.menu-item.danger {
  @apply text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20;
}

.menu-divider {
  @apply h-px bg-gray-200 dark:bg-gray-700 my-1;
}

.line-clamp-2 {
  display: -webkit-box;
  overflow: hidden;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
</style>

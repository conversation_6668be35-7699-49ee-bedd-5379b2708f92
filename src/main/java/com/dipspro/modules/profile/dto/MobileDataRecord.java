package com.dipspro.modules.profile.dto;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 手机号对应的数据记录DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MobileDataRecord implements Serializable {
    private String tableName;
    private Long id;

    @Override
    public String toString() {
        return "MobileDataRecord{" +
                "tableName='" + tableName + '\'' +
                ", id=" + id +
                '}';
    }
} 
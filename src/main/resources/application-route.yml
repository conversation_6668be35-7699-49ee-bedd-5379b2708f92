# =======================================
# 路由转发配置
# =======================================
route:
  services:
    # ANA服务 - 使用Basic认证
    ana:
      enabled: true
      base-url: ${ANA_SERVICE_URL:http://**************:10089/api}
      auth-type: BASIC
      username: ${route.basic_auth.username}
      password: ${route.basic_auth.password}
      connect-timeout: 30000
      read-timeout: 60000
      path-rewrite:
        # 示例: 将 /api/route/ana/v1/data 重写为 /api/v1/data
        "^/api/route/ana(/.*)": "$1"

    # ANA_DIPS服务 - 使用Basic认证
    ana_dips:
      enabled: true
      base-url: ${ANA_DIPS_SERVICE_URL:http://*************:10089/api}
      auth-type: BASIC
      username: ${route.basic_auth.username}
      password: ${route.basic_auth.password}
      connect-timeout: 30000
      read-timeout: 60000
      path-rewrite:
        "^/api/route/ana_dips(/.*)": "$1"

    # ITGVK服务 - 使用Basic认证
    itgvk:
      enabled: true
      base-url: ${ITGVK_SERVICE_URL:http://*************:10105/api}
      auth-type: BASIC
      username: ${route.basic_auth.username}
      password: ${route.basic_auth.password}
      connect-timeout: 30000
      read-timeout: 60000
      path-rewrite:
        "^/api/route/itgvk(/.*)": "$1"

    # EXAM服务 - 使用Basic认证
    exam:
      enabled: true
      base-url: ${EXAM_SERVICE_URL:http://**************:10083/api}
      auth-type: BASIC
      username: ${route.basic_auth.username}
      password: ${route.basic_auth.password}
      connect-timeout: 30000
      read-timeout: 60000
      path-rewrite:
        "^/api/route/exam(/.*)": "$1"

    # DIPSRAG服务 - 无需认证
    dipsrag:
      enabled: true
      base-url: ${DIPSRAG_SERVICE_URL:http://***********:10210/api}
      auth-type: NONE
      connect-timeout: 30000
      read-timeout: 60000
      path-rewrite:
        "^/api/route/dipsrag(/.*)": "$1"

    # JINMAO服务 - 使用Token认证
    jinmao:
      enabled: true
      base-url: ${JINMAO_SERVICE_URL:http://localhost:8084/api}
      auth-type: TOKEN
      token-url: ${JINMAO_TOKEN_URL:http://localhost:8084/oauth/token}
      client-id: ${JINMAO_CLIENT_ID:jinmao_client}
      client-secret: ${JINMAO_CLIENT_SECRET:jinmao_secret}
      connect-timeout: 30000
      read-timeout: 60000
      path-rewrite:
        "^/api/route/jinmao(/.*)": "$1"

    # SP服务 - 使用Basic认证
    sp:
      enabled: true
      base-url: ${SP_SERVICE_URL:http://***********:10109/api}
      auth-type: BASIC
      username: ${route.basic_auth.username}
      password: ${route.basic_auth.password}
      connect-timeout: 30000
      read-timeout: 60000
      path-rewrite:
        "^/api/route/sp(/.*)": "$1"

    # 示例: 其他服务可以继续添加
    # example_service:
    #   enabled: false
    #   base-url: http://localhost:8085
    #   auth-type: JWT  # 使用当前用户的JWT token
    #   connect-timeout: 30000
    #   read-timeout: 60000
package com.dipspro.modules.profile.formatter;

import static com.dipspro.constant.ProjectStatsConstants.DEFAULT_MAX_POI_PER_TYPE;
import static com.dipspro.constant.ProjectStatsConstants.NO_DETAIL_MATCH_MSG;
import static com.dipspro.constant.ProjectStatsConstants.NO_PROJECT_INFO_MSG;
import static com.dipspro.constant.ProjectStatsConstants.PROJECT_LIST_HEADER;
import static com.dipspro.constant.ProjectStatsConstants.PROPERTY_DETAILS_HEADER;
import static com.dipspro.constant.ProjectStatsConstants.USER_PROFILE_HEADER;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;

import com.dipspro.modules.chat.dto.CollapsibleContent;
import com.dipspro.modules.chat.dto.CollapsibleDetail;
import com.dipspro.modules.chat.dto.MessageContent;
import com.dipspro.modules.profile.dto.IndexWithScore;
import com.dipspro.modules.profile.dto.PointOfInterestDto;
import com.dipspro.modules.profile.dto.ProjectDetail;
import com.dipspro.modules.profile.dto.UserProfileDto;
import com.dipspro.modules.profile.dto.UserProfileTag;

/**
 * 项目详情格式化器
 *
 * <AUTHOR>
 */
public class ProjectDetailFormatter {

    private final UserProfileDto statsDto;
    private final boolean showDetail;

    public ProjectDetailFormatter(UserProfileDto statsDto, boolean showDetail) {
        this.statsDto = statsDto;
        this.showDetail = showDetail;
    }

    /**
     * 格式化为结构化的消息内容列表（完整版）
     *
     * @return 结构化的消息内容列表
     */
    public List<MessageContent> formatStructured() {
        List<MessageContent> messageList = new ArrayList<>();

        List<ProjectDetail> projects = statsDto.getProjects();
        List<UserProfileTag> userProfileTags = statsDto.getUserProfileTags();

        if (projects == null || projects.isEmpty()) {
            messageList.add(MessageContent.text(NO_PROJECT_INFO_MSG));
            return messageList;
        }

        // 添加用户画像标签信息（可折叠）
        if (userProfileTags != null && !userProfileTags.isEmpty()) {
            messageList.addAll(buildUserProfileContent(userProfileTags, showDetail));
        }

        // 添加项目列表信息
        if (showDetail) {
            messageList.addAll(buildProjectListContent(projects));

            // 添加项目详情信息（可折叠）
            messageList.addAll(buildProjectDetailsContent(projects));
        }

//        // 添加建议选项
//        messageList.addAll(buildSuggestionsContent());
//
//        // 添加文件附件（如果需要）
//        messageList.addAll(buildFileAttachmentsContent());

        return messageList;
    }

    /**
     * 格式化为完整的文本输出
     *
     * @return 格式化后的文本
     */
    public String format() {
        StringBuilder builder = new StringBuilder();

        List<ProjectDetail> projects = statsDto.getProjects();
        List<UserProfileTag> userProfileTags = statsDto.getUserProfileTags();

        if (projects == null || projects.isEmpty()) {
            builder.append(NO_PROJECT_INFO_MSG);
            return builder.toString();
        }

        // 生成项目列表信息
        builder.append(generateProjectListInfo(projects));
        builder.append("\n\n");

        // 生成用户画像标签信息
        if (userProfileTags != null && !userProfileTags.isEmpty()) {
            builder.append(generateUserProfileTagsInfo(userProfileTags));
            builder.append("\n");
        }

        // 生成项目详情信息
        builder.append(generateProjectDetailsInfo(projects));

        return builder.toString();
    }

    /**
     * 格式化为简单的文本输出，输出画像标签，不输出明细
     *
     * @return
     */
    public String formatSimple() {
        StringBuilder builder = new StringBuilder();

        List<UserProfileTag> userProfileTags = statsDto.getUserProfileTags();
        // 生成用户画像标签信息
        if (userProfileTags != null && !userProfileTags.isEmpty()) {
            builder.append(generateUserProfileTagsInfo(userProfileTags, false));
            builder.append("\n");
        }

        return builder.toString();
    }

    /**
     * 生成项目列表信息
     *
     * @param projects 项目列表
     * @return 项目列表信息字符串
     */
    private String generateProjectListInfo(List<ProjectDetail> projects) {
        StringBuilder builder = new StringBuilder();
        builder.append(PROJECT_LIST_HEADER).append("\n\n");

        int index = 1;
        for (ProjectDetail project : projects) {
            builder.append(index++).append(". ");

            if (StringUtils.isNotBlank(project.getCustomerName())) {
                builder.append(String.format("【%s】 ", project.getCustomerName()));
            }

            builder.append(String.format("**%s**", project.getProjectName()));

            if (StringUtils.isNotBlank(project.getProvince())) {
                builder.append(", ").append(project.getProvince());
            }

            if (project.shouldShowCity()) {
                builder.append(", ").append(project.getCity());
            }

            if (project.hasValidSignDate()) {
                builder.append(", 签约日期：").append(project.getSignDatetime());
            }

            builder.append("\n");
        }

        return builder.toString();
    }

    /**
     * 生成用户画像标签信息（默认不输出项目明细）
     *
     * @param userProfileTags 用户画像标签列表
     * @return 用户画像标签信息字符串
     */
    private String generateUserProfileTagsInfo(List<UserProfileTag> userProfileTags) {
        return generateUserProfileTagsInfo(userProfileTags, true);
    }

    /**
     * 生成用户画像标签信息
     *
     * @param userProfileTags   用户画像标签列表
     * @param withProjectDetail 是否输出匹配项目明细
     * @return 用户画像标签信息字符串
     */
    private String generateUserProfileTagsInfo(List<UserProfileTag> userProfileTags, boolean withProjectDetail) {
        StringBuilder builder = new StringBuilder();
        builder.append(USER_PROFILE_HEADER).append("\n\n");

        // 按标签类型分组
        Map<String, List<UserProfileTag>> tagsByType = userProfileTags.stream()
                .collect(Collectors.groupingBy(UserProfileTag::getType));

        for (Map.Entry<String, List<UserProfileTag>> entry : tagsByType.entrySet()) {
            String type = entry.getKey();
            List<UserProfileTag> typeTags = entry.getValue();

            builder.append("- ").append(type).append("：\n");

            for (UserProfileTag tag : typeTags) {
                builder.append("  * **").append(tag.getName()).append("**：");
                builder.append(tag.getDescription());

                // 根据 withProjectDetail 参数决定是否添加匹配的项目名称
                if (withProjectDetail && !tag.getMatchedProjectNames().isEmpty()) {
                    builder.append("（匹配项目：");
                    builder.append(String.join("、", tag.getMatchedProjectNames()));
                    builder.append("）");
                }

                builder.append("\n");
            }

            builder.append("\n");
        }

        return builder.toString();
    }

    /**
     * 生成项目详情信息
     *
     * @param projects 项目列表
     * @return 项目详情信息字符串
     */
    private String generateProjectDetailsInfo(List<ProjectDetail> projects) {
        StringBuilder builder = new StringBuilder();
        builder.append(PROPERTY_DETAILS_HEADER).append("\n\n");

        int index = 1;
        for (ProjectDetail project : projects) {
            builder.append(index++).append(". ")
                    .append("**").append(project.getProjectName()).append("**: ");

            if (!project.isMatchedLianjia()) {
                builder.append(NO_DETAIL_MATCH_MSG).append("\n");
            } else {
                builder.append("\n");
                appendProjectGradeAndType(builder, project);
                appendProjectTypeAndPrice(builder, project);
                appendPropertyFeeInfo(builder, project);
                appendFacilitiesInfo(builder, project);
            }

            builder.append("\n\n");
            appendRatingInfo(builder, project);

            // 添加指标得分信息
            appendIndexScoreInfo(builder, project);
        }

        return builder.toString();
    }

    /**
     * 添加项目楼盘档次、房屋类型、装修类型
     *
     * @param builder StringBuilder对象
     * @param project 项目详情
     */
    private void appendProjectGradeAndType(StringBuilder builder, ProjectDetail project) {
        boolean hasGrade = StringUtils.isNotBlank(project.getBuildingGrade());
        boolean hasHouseType = StringUtils.isNotBlank(project.getHouseType());
        boolean hasDecorationType = StringUtils.isNotBlank(project.getDecorationType());
        boolean hasOutput = false;

        if (hasGrade) {
            builder.append("   - 楼盘档次：").append(project.getBuildingGrade());
            hasOutput = true;
        }

        if (hasHouseType) {
            if (hasGrade) {
                builder.append("，房屋类型：").append(project.getHouseType());
            } else {
                builder.append("   - 房屋类型：").append(project.getHouseType());
            }
            hasOutput = true;
        }

        if (hasDecorationType) {
            if (hasGrade || hasHouseType) {
                builder.append("，装修类型：").append(project.getDecorationType());
            } else {
                builder.append("   - 装修类型：").append(project.getDecorationType());
            }
            hasOutput = true;
        }

        if (hasOutput) {
            builder.append("\n");
        }
    }

    /**
     * 添加项目类型和价格信息
     *
     * @param builder StringBuilder对象
     * @param project 项目详情
     */
    private void appendProjectTypeAndPrice(StringBuilder builder, ProjectDetail project) {
        boolean hasType = StringUtils.isNotBlank(project.getLjXiaoquBuildType());
        boolean hasPrice = StringUtils.isNotBlank(project.getLjLoupanPriceAverage());
        boolean hasOutput = false;

        if (hasType) {
            builder.append("   - 类型：").append(project.getLjXiaoquBuildType());
            hasOutput = true;
        }

        if (hasPrice) {
            if (hasType) {
                builder.append("，均价：").append(project.getFormattedPrice());
            } else {
                builder.append("   - 均价：").append(project.getFormattedPrice());
            }
            hasOutput = true;
        }

        if (hasOutput) {
            builder.append("\n");
        }
    }

    /**
     * 添加物业费信息
     *
     * @param builder StringBuilder对象
     * @param project 项目详情
     */
    private void appendPropertyFeeInfo(StringBuilder builder, ProjectDetail project) {
        boolean hasCostLevel = StringUtils.isNotBlank(project.getPropertyCostLevel());
        boolean hasPropertyFee = StringUtils.isNotBlank(project.getLjXiaoquPropertyFeeDesc());
        boolean hasOutput = false;

        if (hasCostLevel) {
            builder.append("   - 物业费水平：").append(project.getPropertyCostLevel());
            hasOutput = true;
        }

        if (hasPropertyFee) {
            if (hasCostLevel) {
                builder.append("，物业费：").append(project.getLjXiaoquPropertyFeeDesc());
            } else {
                builder.append("   - 物业费：").append(project.getLjXiaoquPropertyFeeDesc());
            }
            hasOutput = true;
        }

        if (hasOutput) {
            builder.append("\n");
        }
    }

    /**
     * 添加周边配套信息
     *
     * @param builder StringBuilder对象
     * @param project 项目详情
     */
    private void appendFacilitiesInfo(StringBuilder builder, ProjectDetail project) {
        // 只有当有至少一个非null的设施时才输出周边配套部分
        if (project.hasAnyFacilities()) {
            String facilitiesDesc = project.getFacilitiesDescription();
            if (StringUtils.isNotBlank(facilitiesDesc)) {
                builder.append("   - 周边配套统计：").append(facilitiesDesc).append("\n");
            }
        }

        // 添加周边POI信息
        if (project.hasNearbyPois()) {
            String poisDesc = project.getNearbyPoisDescription(DEFAULT_MAX_POI_PER_TYPE); // 每种类型显示最多5个具体POI
            if (StringUtils.isNotBlank(poisDesc)) {
                builder.append("   - 周边配套：").append(poisDesc);
            }
        }
    }

    /**
     * 添加评分和评价信息
     *
     * @param builder StringBuilder对象
     * @param project 项目详情
     */
    private void appendRatingInfo(StringBuilder builder, ProjectDetail project) {
        boolean hasRa1a5Score = StringUtils.isNotBlank(project.getRa1a5Score());
        boolean hasTextGood = StringUtils.isNotBlank(project.getTextGood());
        boolean hasTextBad = StringUtils.isNotBlank(project.getTextBad());

        if (hasRa1a5Score) {
            builder.append("   - 总体满意度评分：").append(project.getRa1a5Score()).append("\n");
        }

        if (hasTextGood) {
            builder.append("   - 开放题正向：").append(project.getTextGood()).append("\n");
        }

        if (hasTextBad) {
            builder.append("   - 开放题负向：").append(project.getTextBad()).append("\n");
        }
    }

    /**
     * 添加指标得分信息
     *
     * @param builder StringBuilder对象
     * @param project 项目详情
     */
    private void appendIndexScoreInfo(StringBuilder builder, ProjectDetail project) {
        // 输出高分指标(4-5分)
        if (!project.getHighScoreIndices().isEmpty()) {
            builder.append("   - 高分指标(4-5分)：");

            for (int i = 0; i < project.getHighScoreIndices().size(); i++) {
                IndexWithScore index = project.getHighScoreIndices().get(i);

                if (i > 0) {
                    builder.append("、");
                }

                builder.append(index.getIndexGridName())
                        .append("(").append(index.getScore()).append("分)");
            }

            builder.append("\n");
        }

        // 输出低分指标(2-3分)
        if (!project.getLowScoreIndices().isEmpty()) {
            builder.append("   - 低分指标(2-3分)：");

            for (int i = 0; i < project.getLowScoreIndices().size(); i++) {
                IndexWithScore index = project.getLowScoreIndices().get(i);

                if (i > 0) {
                    builder.append("、");
                }

                builder.append(index.getIndexGridName())
                        .append("(").append(index.getScore()).append("分)");
            }

            builder.append("\n");
        }

        // 输出极低分指标(1分)
        if (!project.getVeryLowScoreIndices().isEmpty()) {
            builder.append("   - 极低分指标(1分)：");

            for (int i = 0; i < project.getVeryLowScoreIndices().size(); i++) {
                IndexWithScore index = project.getVeryLowScoreIndices().get(i);

                if (i > 0) {
                    builder.append("、");
                }

                builder.append(index.getIndexGridName())
                        .append("(").append(index.getScore()).append("分)");
            }

            builder.append("\n");
        }

        // 将低分指标和极低分指标合并处理
        List<IndexWithScore> allLowScoreIndices = new ArrayList<>();
        allLowScoreIndices.addAll(project.getMainLowScoreIndices());

        // 创建主指标到子指标的映射
        Map<String, List<IndexWithScore>> mainToSubIndices = new HashMap<>();

        // 提取所有主指标
        List<IndexWithScore> mainIndices = new ArrayList<>();
        for (IndexWithScore index : project.getMainLowScoreIndices()) {
            mainIndices.add(index);
            mainToSubIndices.put(index.getIndexCode(), new ArrayList<>());
        }

        // 将子指标关联到对应的主指标
        for (IndexWithScore subIndex : project.getSubLowScoreIndices()) {
            if (StringUtils.isNotBlank(subIndex.getParentIndexCode()) &&
                    mainToSubIndices.containsKey(subIndex.getParentIndexCode())) {
                mainToSubIndices.get(subIndex.getParentIndexCode()).add(subIndex);
            }
        }

        // 输出主要低分指标及其子指标
        if (!mainIndices.isEmpty()) {
            builder.append("   - 主要低分指标：").append("\n");

            for (IndexWithScore mainIndex : mainIndices) {
                builder.append("      * ")
                        .append(mainIndex.getIndexGridName())
                        .append("(").append(mainIndex.getScore()).append("分)")
                        .append("\n");

                // 显示该主指标的子指标（如果有）
                List<IndexWithScore> subIndices = mainToSubIndices.get(mainIndex.getIndexCode());
                if (subIndices != null && !subIndices.isEmpty()) {
                    for (IndexWithScore subIndex : subIndices) {
                        builder.append("          * ")
                                .append(subIndex.getIndexGridName())
                                .append("(").append(subIndex.getScore()).append("分)")
                                .append("\n");
                    }
                }
            }
        }
    }

    /**
     * 构建项目列表内容
     */
    private List<MessageContent> buildProjectListContent(List<ProjectDetail> projects) {
        List<MessageContent> contentList = new ArrayList<>();
        CollapsibleContent collapsibleContent = CollapsibleContent.withSummary("历史购买");
        collapsibleContent.addDetail(CollapsibleDetail.simple(generateProjectListInfo(projects)));
        contentList.add(MessageContent.collapsible(collapsibleContent));
        return contentList;
    }

    /**
     * 构建用户画像内容
     *
     * @param userProfileTags 用户画像标签列表
     * @param isDetailed      是否显示项目明细，根据角色来决定是否显示
     * @return 用户画像内容列表
     */
    private List<MessageContent> buildUserProfileContent(List<UserProfileTag> userProfileTags, boolean isDetailed) {
        List<MessageContent> contentList = new ArrayList<>();
        contentList.add(MessageContent.text("#### 用户画像标签"));

        /*
         * 用户画像标签内容
         */
        // 按标签类型分组
        Map<String, List<UserProfileTag>> tagsByType = userProfileTags.stream()
                .collect(Collectors.groupingBy(UserProfileTag::getType));

        for (Map.Entry<String, List<UserProfileTag>> entry : tagsByType.entrySet()) {
            String type = entry.getKey();

            contentList.add(MessageContent.text(String.format("  %s", type)));

            CollapsibleContent collapsibleContent = CollapsibleContent.create();

            entry.getValue().forEach(tag -> {
                String title = String.format("    %s: %s", tag.getName(), tag.getDescription());
                if (isDetailed) {
                    String content = String.format("匹配项目: %s", String.join("， ", tag.getMatchedProjectNames()));
                    collapsibleContent.addDetail(CollapsibleDetail.expandable(title, content));
                } else {
                    collapsibleContent.addDetail(CollapsibleDetail.simple(title));
                }
            });
            
            contentList.add(MessageContent.collapsible(collapsibleContent));
        }

        /*
         * 用户历史购买信息
         */

        /*
         * 用户历史购买物业详情
         */

        return contentList;
    }

    /**
     * 构建用户画像文本
     */
    private String buildUserProfileText(List<UserProfileTag> userProfileTags) {
        StringBuilder profileText = new StringBuilder();
        profileText.append(USER_PROFILE_HEADER).append("\n\n");

        for (UserProfileTag tag : userProfileTags) {
            profileText.append(String.format("- **%s**: %s\n", tag.getName(), tag.getDescription()));
        }

        return profileText.toString();
    }

    /**
     * 构建项目详情内容
     */
    private List<MessageContent> buildProjectDetailsContent(List<ProjectDetail> projects) {
        List<MessageContent> contentList = new ArrayList<>();
        CollapsibleContent collapsibleContent = CollapsibleContent.withSummary("项目详细信息");
        collapsibleContent.addDetail(CollapsibleDetail.simple(generateProjectDetailsInfo(projects)));
        contentList.add(MessageContent.collapsible(collapsibleContent));
        return contentList;
    }

    /**
     * 构建单个项目详情文本
     */
    private String buildProjectDetailText(ProjectDetail project) {
        StringBuilder detailText = new StringBuilder();

        detailText.append("**项目名称**: ").append(project.getProjectName()).append("\n\n");

        if (project.getNearbyPois() != null && !project.getNearbyPois().isEmpty()) {
            detailText.append(PROPERTY_DETAILS_HEADER).append("\n");

            // 使用nearbyPois字段替代poiDetails
            List<PointOfInterestDto> pois = project.getNearbyPois();

            // 按类型分组（如果PointOfInterestDto有type字段）
            detailText.append("\n**周边设施**:\n");

            int count = 0;
            for (PointOfInterestDto poi : pois) {
                if (count >= DEFAULT_MAX_POI_PER_TYPE) break;
                detailText.append(String.format("- %s\n", poi.getName()));
                count++;
            }

            if (pois.size() > DEFAULT_MAX_POI_PER_TYPE) {
                detailText.append(String.format("... 还有 %d 个设施\n",
                        pois.size() - DEFAULT_MAX_POI_PER_TYPE));
            }
        } else {
            detailText.append(NO_DETAIL_MATCH_MSG);
        }

        return detailText.toString();
    }

    /**
     * 构建文件附件内容
     */
    private List<MessageContent> buildFileAttachmentsContent() {
        List<MessageContent> contentList = new ArrayList<>();

        // 创建示例文件附件
        List<Map<String, Object>> files = new ArrayList<>();

        Map<String, Object> reportFile = new HashMap<>();
        reportFile.put("uid", "project-report-" + System.currentTimeMillis());
        reportFile.put("name", "项目详细报告.pdf");
        reportFile.put("size", 2048576);
        reportFile.put("status", "done");
        reportFile.put("description", "包含所有项目的详细分析报告");
        files.add(reportFile);

        Map<String, Object> dataFile = new HashMap<>();
        dataFile.put("uid", "user-profile-" + System.currentTimeMillis());
        dataFile.put("name", "用户画像数据.xlsx");
        dataFile.put("size", 1024000);
        dataFile.put("status", "done");
        dataFile.put("description", "用户偏好和行为分析数据");
        files.add(dataFile);

        contentList.add(MessageContent.file(files));
        return contentList;
    }
}
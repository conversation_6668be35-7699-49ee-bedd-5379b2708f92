package com.dipspro.modules.profile.dto;

import java.time.LocalDateTime;
import java.util.List;

import lombok.Data;

/**
 * 手机号关联的名单和数据集统计信息DTO
 */
@Data
public class MobileCombinedStatsDTO {
    private String mobile;
    private NamelistStats namelistStats;
    private DatasetStats datasetStats;
    private Summary summary;

    /**
     * 名单统计信息
     */
    @Data
    public static class NamelistStats {
        private int total;
        private List<SourceCount> bySource;
        private List<CustomerProjectCount> byCustomerProject;
        private TimeRange byCreateTime;
    }

    /**
     * 数据集统计信息
     */
    @Data
    public static class DatasetStats {
        private int total;
        private List<TypeCount> byDatasetType;
        private List<CustomerProjectCount> byCustomerProject;
        private TimeRange byCreateTime;
    }

    /**
     * 汇总信息
     */
    @Data
    public static class Summary {
        private int totalRecords;
        private CustomerProjectStats customerProjectStats;
        private TimeRange timeRange;
    }

    /**
     * 客户项目统计信息
     */
    @Data
    public static class CustomerProjectStats {
        private int totalProjects;
        private List<ProjectDetail> projects;
    }

    /**
     * 项目详情
     */
    @Data
    public static class ProjectDetail {
        private String customerProject;
        private int namelistCount;
        private int datasetCount;
        private int totalCount;
        private String province;  // 省份
        private String city;      // 城市
        private String signDatetime;  // 签约时间
        private String buildingGrade;  // 楼盘档次
        private String houseType;      // 房屋类型
        private String decorationType; // 装修类型
        private String houseNo;        // 房号
    }

    /**
     * 来源统计
     */
    @Data
    public static class SourceCount {
        private String source;
        private int count;
    }

    /**
     * 类型统计
     */
    @Data
    public static class TypeCount {
        private String type;
        private int count;
    }

    /**
     * 客户项目统计
     */
    @Data
    public static class CustomerProjectCount {
        private String customerProject;
        private int count;
    }

    /**
     * 时间范围
     */
    @Data
    public static class TimeRange {
        private LocalDateTime earliest;
        private LocalDateTime latest;
    }
} 
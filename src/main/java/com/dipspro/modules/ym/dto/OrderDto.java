package com.dipspro.modules.ym.dto;

import lombok.Builder;
import lombok.Data;

/**
 * 医美 - 微信 ID 与订单映射 DTO
 * 没有真实数据，仅做测试使用
 */
@Data
@Builder
public class OrderDto {

    private Long id;
    // 微信 wx_open_id
    private String wxOpenId;
    // 微信 unionId
    private String unionId;
    // 机构 Id
    private String orgId;
    // 商品
    private ProductDto product;
    // 购买价格
    private float buyPrice;
    // 时间周期开始时间
    private String startDate;
    // 时间周期结束时间
    private String endDate;

}

package com.dipspro.modules.embedding.service;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

import com.dipspro.modules.embedding.dto.PromptTemplateVectorDto;

/**
 * 提示词模板向量服务接口
 */
public interface PromptTemplateVectorService {

    /**
     * 获取所有向量
     */
    List<PromptTemplateVectorDto> getAllVectors(String user);

    /**
     * 根据ID获取向量
     */
    Optional<PromptTemplateVectorDto> getVectorById(UUID id, String user);

    /**
     * 根据模板ID获取向量列表
     */
    List<PromptTemplateVectorDto> getVectorByTemplateId(UUID templateId, String user);

    /**
     * 批量查询向量
     */
    List<PromptTemplateVectorDto> getVectorsByIds(List<UUID> ids, String user);

    /**
     * 根据模板ID列表批量获取向量
     */
    List<PromptTemplateVectorDto> getVectorsByTemplateIds(List<UUID> templateIds, String user);

    /**
     * 创建向量
     */
    PromptTemplateVectorDto createVector(PromptTemplateVectorDto vectorDto, String user);

    /**
     * 更新向量
     */
    Optional<PromptTemplateVectorDto> updateVector(UUID id, PromptTemplateVectorDto vectorDto, String user);

    /**
     * 删除向量
     */
    boolean deleteVector(UUID id, String user);

    /**
     * 根据模板ID删除向量
     */
    boolean deleteVectorByTemplateId(UUID templateId, String user);

    /**
     * 检查模板ID是否已存在向量
     */
    boolean existsByTemplateId(UUID templateId, String user);

    /**
     * 根据创建者查找向量
     */
    List<PromptTemplateVectorDto> getVectorsByCreatedBy(String createdBy, String user);

    /**
     * 根据典型描述模糊查询
     */
    List<PromptTemplateVectorDto> searchVectorsByDescription(String description, String user);

    /**
     * 计算两个向量的相似度
     */
    double calculateSimilarity(UUID vectorId1, UUID vectorId2, String user);

    /**
     * 计算向量与给定文本的相似度
     */
    double calculateSimilarityWithText(UUID vectorId, String text, String user);

    /**
     * 查找与给定文本最相似的向量
     */
    List<PromptTemplateVectorDto> findSimilarVectors(String text, int limit, String user);

    /**
     * 查找与给定向量最相似的向量
     */
    List<PromptTemplateVectorDto> findSimilarVectors(UUID vectorId, int limit, String user);

    /**
     * 批量创建向量
     */
    List<PromptTemplateVectorDto> createVectorsBatch(List<PromptTemplateVectorDto> vectorDtos, String user);

    /**
     * 根据序列ID范围查询（用于分批处理）
     */
    List<PromptTemplateVectorDto> getVectorsBySerialIdRange(Long startId, Long endId, String user);

    /**
     * 获取指定数量的最新向量
     */
    List<PromptTemplateVectorDto> getLatestVectors(int limit, String user);

    /**
     * 根据文本描述找模板，每个模板ID仅保留最高的相似度
     */
    List<PromptTemplateVectorDto> findSimilarTemplates(String text, int limit, String user);
}
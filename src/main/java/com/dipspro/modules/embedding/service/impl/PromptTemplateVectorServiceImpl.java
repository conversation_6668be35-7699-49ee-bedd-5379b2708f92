package com.dipspro.modules.embedding.service.impl;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.dipspro.modules.embedding.dto.PromptTemplateVectorDto;
import com.dipspro.modules.embedding.entity.PromptTemplateVector;
import com.dipspro.modules.embedding.mapper.PromptTemplateVectorMapper;
import com.dipspro.modules.embedding.repository.PromptTemplateVectorRepository;
import com.dipspro.modules.embedding.service.EmbeddingService;
import com.dipspro.modules.embedding.service.PromptTemplateVectorService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@RequiredArgsConstructor
@Slf4j
public class PromptTemplateVectorServiceImpl implements PromptTemplateVectorService {

    private final PromptTemplateVectorRepository promptTemplateVectorRepository;
    private final EmbeddingService embeddingService;
    private final PromptTemplateVectorMapper mapper;

    @Override
    @Transactional(readOnly = true)
    public List<PromptTemplateVectorDto> getAllVectors(String user) {
        log.debug("Fetching all prompt template vectors for user: {}", user);
        return promptTemplateVectorRepository.findAllByOrderByCreatedAtDesc().stream()
                .map(mapper::toDto)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<PromptTemplateVectorDto> getVectorById(UUID id, String user) {
        log.debug("Fetching prompt template vector by id: {} for user: {}", id, user);
        return promptTemplateVectorRepository.findById(id)
                .map(mapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public List<PromptTemplateVectorDto> getVectorByTemplateId(UUID templateId, String user) {
        log.debug("Fetching prompt template vectors by template id: {} for user: {}", templateId, user);
        return promptTemplateVectorRepository.findByTemplateId(templateId).stream()
                .map(mapper::toDto)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<PromptTemplateVectorDto> getVectorsByIds(List<UUID> ids, String user) {
        log.debug("Fetching prompt template vectors by ids: {} for user: {}", ids, user);
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return promptTemplateVectorRepository.findByIdIn(ids).stream()
                .map(mapper::toDto)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<PromptTemplateVectorDto> getVectorsByTemplateIds(List<UUID> templateIds, String user) {
        log.debug("Fetching prompt template vectors by template ids: {} for user: {}", templateIds, user);
        if (CollectionUtils.isEmpty(templateIds)) {
            return Collections.emptyList();
        }
        return promptTemplateVectorRepository.findByTemplateIdIn(templateIds).stream()
                .map(mapper::toDto)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public PromptTemplateVectorDto createVector(PromptTemplateVectorDto vectorDto, String user) {
        log.info("Creating new prompt template vector for template: {} by user: {}", vectorDto.getTemplateId(), user);

        // Auto-generate embedding from typicalDescription if not provided
        if (vectorDto.getEmbedding() == null && vectorDto.getTypicalDescription() != null) {
            float[] embedding = embeddingService.encode(vectorDto.getTypicalDescription());
            vectorDto.setEmbedding(embedding);
        }
        vectorDto.setCreatedBy(user);
        vectorDto.setUpdatedBy(user);
        PromptTemplateVector vector = mapper.toEntity(vectorDto);
        vector.setId(null); // Ensure new entity
        PromptTemplateVector savedVector = promptTemplateVectorRepository.save(vector);
        log.info("Created prompt template vector with id: {} by user: {}", savedVector.getId(), user);
        return mapper.toDto(savedVector);
    }

    @Override
    @Transactional
    public Optional<PromptTemplateVectorDto> updateVector(UUID id, PromptTemplateVectorDto vectorDto, String user) {
        log.info("Updating prompt template vector with id: {} by user: {}", id, user);
        return promptTemplateVectorRepository.findById(id)
                .map(existingVector -> {
                    existingVector.setTemplateId(vectorDto.getTemplateId());
                    existingVector.setTypicalDescription(vectorDto.getTypicalDescription());
                    existingVector.setUpdatedBy(user);
                    existingVector.setUpdatedAt(Instant.now());

                    // Auto-regenerate embedding from typicalDescription
                    if (vectorDto.getTypicalDescription() != null) {
                        float[] newEmbedding = embeddingService.encode(vectorDto.getTypicalDescription());
                        existingVector.setEmbedding(newEmbedding);
                    } else if (vectorDto.getEmbedding() != null) {
                        existingVector.setEmbedding(vectorDto.getEmbedding());
                    }

                    PromptTemplateVector updatedVector = promptTemplateVectorRepository.save(existingVector);
                    log.info("Successfully updated vector with id: {} by user: {}", updatedVector.getId(), user);
                    return mapper.toDto(updatedVector);
                });
    }

    @Override
    @Transactional
    public boolean deleteVector(UUID id, String user) {
        log.warn("Attempting to delete prompt template vector with id: {} by user: {}", id, user);
        if (promptTemplateVectorRepository.existsById(id)) {
            promptTemplateVectorRepository.deleteById(id);
            log.warn("Successfully deleted prompt template vector with id: {} by user: {}", id, user);
            return true;
        } else {
            log.warn("Prompt template vector with id: {} not found for deletion by user: {}", id, user);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean deleteVectorByTemplateId(UUID templateId, String user) {
        log.warn("Attempting to delete prompt template vector with template id: {} by user: {}", templateId, user);
        if (promptTemplateVectorRepository.existsByTemplateId(templateId)) {
            promptTemplateVectorRepository.deleteByTemplateId(templateId);
            log.warn("Successfully deleted prompt template vector with template id: {} by user: {}", templateId, user);
            return true;
        } else {
            log.warn("Prompt template vector with template id: {} not found for deletion by user: {}", templateId,
                    user);
            return false;
        }
    }

    @Override
    @Transactional(readOnly = true)
    public boolean existsByTemplateId(UUID templateId, String user) {
        log.debug("Checking if vector exists for template id: {} by user: {}", templateId, user);
        return promptTemplateVectorRepository.existsByTemplateId(templateId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<PromptTemplateVectorDto> getVectorsByCreatedBy(String createdBy, String user) {
        log.debug("Fetching vectors by created by: {} for user: {}", createdBy, user);
        return promptTemplateVectorRepository.findByCreatedByOrderByCreatedAtDesc(createdBy).stream()
                .map(mapper::toDto)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<PromptTemplateVectorDto> searchVectorsByDescription(String description, String user) {
        log.debug("Searching vectors by description: {} for user: {}", description, user);
        return promptTemplateVectorRepository.findByTypicalDescriptionContaining(description).stream()
                .map(mapper::toDto)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public double calculateSimilarity(UUID vectorId1, UUID vectorId2, String user) {
        log.debug("Calculating similarity between vectors: {} and {} for user: {}", vectorId1, vectorId2, user);

        Optional<PromptTemplateVector> vector1 = promptTemplateVectorRepository.findById(vectorId1);
        Optional<PromptTemplateVector> vector2 = promptTemplateVectorRepository.findById(vectorId2);

        if (vector1.isEmpty() || vector2.isEmpty()) {
            throw new RuntimeException("向量不存在");
        }

        return embeddingService.cosineSimilarity(vector1.get().getEmbedding(), vector2.get().getEmbedding());
    }

    @Override
    @Transactional(readOnly = true)
    public double calculateSimilarityWithText(UUID vectorId, String text, String user) {
        log.debug("Calculating similarity between vector {} and text for user: {}", vectorId, user);

        Optional<PromptTemplateVector> vector = promptTemplateVectorRepository.findById(vectorId);
        if (vector.isEmpty()) {
            throw new RuntimeException("向量不存在");
        }

        float[] textEmbedding = embeddingService.encode(text);
        return embeddingService.cosineSimilarity(vector.get().getEmbedding(), textEmbedding);
    }

    @Override
    @Transactional(readOnly = true)
    public List<PromptTemplateVectorDto> findSimilarVectors(String text, int limit, String user) {
        log.debug("Finding similar vectors for text with limit: {} for user: {}", limit, user);

        float[] textEmbedding = embeddingService.encode(text);
        List<PromptTemplateVector> allVectors = promptTemplateVectorRepository.findAll();

        return allVectors.stream()
                .map(vector -> {
                    double similarity = embeddingService.cosineSimilarity(
                            textEmbedding,
                            vector.getEmbedding());
                    PromptTemplateVectorDto dto = mapper.toDto(vector);
                    dto.setSimilarity(similarity);
                    return dto;
                })
                .sorted((a, b) -> Double.compare(b.getSimilarity(), a.getSimilarity()))
                .limit(limit)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<PromptTemplateVectorDto> findSimilarVectors(UUID vectorId, int limit, String user) {
        log.debug("Finding similar vectors for vector: {} with limit: {} for user: {}", vectorId, limit, user);

        Optional<PromptTemplateVector> targetVector = promptTemplateVectorRepository.findById(vectorId);
        if (targetVector.isEmpty()) {
            log.warn("Target vector not found: {} for user: {}", vectorId, user);
            return Collections.emptyList();
        }

        float[] targetEmbedding = targetVector.get().getEmbedding();
        List<PromptTemplateVector> allVectors = promptTemplateVectorRepository.findAll();

        return allVectors.stream()
                .filter(vector -> !vector.getId().equals(vectorId)) // Exclude the target vector itself
                .map(vector -> {
                    double similarity = embeddingService.cosineSimilarity(
                            targetEmbedding,
                            vector.getEmbedding());
                    PromptTemplateVectorDto dto = mapper.toDto(vector);
                    dto.setSimilarity(similarity);
                    return dto;
                })
                .sorted((a, b) -> Double.compare(b.getSimilarity(), a.getSimilarity()))
                .limit(limit)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public List<PromptTemplateVectorDto> createVectorsBatch(List<PromptTemplateVectorDto> vectorDtos, String user) {
        log.info("Creating batch of {} vectors for user: {}", vectorDtos.size(), user);

        // Auto-generate embeddings for DTOs that don't have them
        List<String> textsToEncode = new ArrayList<>();
        List<Integer> indicesNeedingEmbedding = new ArrayList<>();

        for (int i = 0; i < vectorDtos.size(); i++) {
            PromptTemplateVectorDto dto = vectorDtos.get(i);
            if (dto.getEmbedding() == null && dto.getTypicalDescription() != null) {
                textsToEncode.add(dto.getTypicalDescription());
                indicesNeedingEmbedding.add(i);
            }
        }

        // Batch encode if needed
        if (!textsToEncode.isEmpty()) {
            List<float[]> embeddings = embeddingService.encodeBatchParallel(textsToEncode).join();
            for (int i = 0; i < indicesNeedingEmbedding.size(); i++) {
                int dtoIndex = indicesNeedingEmbedding.get(i);
                vectorDtos.get(dtoIndex).setEmbedding(embeddings.get(i));
            }
        }

        List<PromptTemplateVector> entities = vectorDtos.stream()
                .map(dto -> {
                    PromptTemplateVector entity = mapper.toEntity(dto);
                    entity.setId(null); // Ensure new entities
                    return entity;
                })
                .collect(Collectors.toList());

        List<PromptTemplateVector> savedEntities = promptTemplateVectorRepository.saveAll(entities);
        log.info("Successfully created {} vectors for user: {}", savedEntities.size(), user);

        return savedEntities.stream()
                .map(mapper::toDto)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<PromptTemplateVectorDto> getVectorsBySerialIdRange(Long startId, Long endId, String user) {
        log.debug("Fetching vectors by serial id range: {} to {} for user: {}", startId, endId, user);
        return promptTemplateVectorRepository.findBySerialIdBetween(startId, endId).stream()
                .map(mapper::toDto)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<PromptTemplateVectorDto> getLatestVectors(int limit, String user) {
        log.debug("Fetching latest {} vectors for user: {}", limit, user);
        return promptTemplateVectorRepository.findTopByOrderByCreatedAtDesc(limit).stream()
                .map(mapper::toDto)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<PromptTemplateVectorDto> findSimilarTemplates(String text, int limit, String user) {
        log.debug("Finding similar templates for text with limit: {} for user: {}", limit, user);

        if (text == null || text.trim().isEmpty()) {
            log.warn("Input text is null or empty for user: {}", user);
            return Collections.emptyList();
        }

        try {
            // 1. 将文本描述转化为向量
            float[] textEmbedding = embeddingService.encode(text.trim());
            log.debug("Generated embedding for input text, dimension: {}", textEmbedding.length);

            // 2. 获取所有模板向量
            List<PromptTemplateVector> allVectors = promptTemplateVectorRepository.findAll();
            if (allVectors.isEmpty()) {
                log.debug("No template vectors found for user: {}", user);
                return Collections.emptyList();
            }

            // 3. 计算相似度并按模板ID分组，每个模板只保留最高相似度的向量
            Map<UUID, PromptTemplateVectorDto> templateBestMatches = new HashMap<>();

            for (PromptTemplateVector vector : allVectors) {
                if (vector.getEmbedding() == null) {
                    log.debug("Skipping vector {} with null embedding", vector.getId());
                    continue;
                }

                // 计算相似度
                double similarity = embeddingService.cosineSimilarity(textEmbedding, vector.getEmbedding());

                PromptTemplateVectorDto dto = mapper.toDto(vector);
                dto.setSimilarity(similarity);

                UUID templateId = vector.getTemplateId();

                // 4. 每个模板只取相似度最高的1个
                if (!templateBestMatches.containsKey(templateId) ||
                        templateBestMatches.get(templateId).getSimilarity() < similarity) {
                    templateBestMatches.put(templateId, dto);
                }
            }

            // 5. 按相似度降序排序并返回前N个
            List<PromptTemplateVectorDto> result = templateBestMatches.values().stream()
                    .sorted((a, b) -> Double.compare(b.getSimilarity(), a.getSimilarity()))
                    .limit(limit)
                    .collect(Collectors.toList());

            log.debug("Found {} similar templates for user: {}", result.size(), user);
            return result;

        } catch (Exception e) {
            log.error("Error finding similar templates for user: {}", user, e);
            throw new RuntimeException("查找相似模板失败: " + e.getMessage(), e);
        }
    }
}
<script setup lang="ts">
import {defineComponent, h, markRaw, ref, shallowRef, onMounted} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import SubjectVisual from '@/components/visual/SubjectVisual.vue'
import Empty from "@/components/visual/Empty.vue"
import storage from '@/utils/storage'
import {ElMessage} from "element-plus"
import {Modal} from "ant-design-vue";
import * as visualApi from "@/api/modules/visual.ts"

defineOptions({
  name: 'Report',
})

const props = defineProps<{
  isSpecific: boolean
  params?: any
}>()

const searchForm = ref<any>({})
const visualList = ref<any[]>([])
const filterVisualList = ref<any[]>([])
const vertexId = ref(storage.local.get('vertexId') ?? '')
const orgCategoryTierId = ref(storage.local.get('orgCategoryTierId') ?? '')
const deptId = ref(storage.local.get('deptId') ?? '')
const roleId = ref(storage.local.get('roleId') ?? '')
const activePage = shallowRef(Empty)
const selectedRowKey = ref<string>('')
const currentVisual = ref<any>(null)
const visualData = ref({})
const pptPath = ref<string>('')
const pptExists = ref(false)
const isLoading = ref(false)
const isInitializing = ref(false)
const router = useRouter()
const route = useRoute()

/**
 * 跳转到分析报告页面
 */
const navigateToAnalysisReport = () => {
  router.push('/analysis/report')
}

const columns = [
  {
    title: 'index',
    dataIndex: 'index',
    width: 50,
    customRender: ({index}: { index: number }) => index + 1
  },
  {
    title: 'officialName',
    dataIndex: 'officialName',
    width: 200
  }
]

onMounted(() => {
  console.log('Report组件挂载，props.isSpecific:', props.isSpecific)
  if (props.isSpecific) {
    loadSpecificVisuals()
  }
  else {
    console.log('设置初始化标志为true，开始搜索报告')
    isInitializing.value = true
    searchDpCustomerReport()
  }
})

const searchDpCustomerReport = async () => {
  console.log('开始搜索报告，isInitializing:', isInitializing.value)
  searchForm.value.reports = []

  try {
    const result = await visualApi.searchDpCustomerReport(
      orgCategoryTierId.value,
      deptId.value,
      roleId.value
    )

    console.log('报告搜索结果:', result)
    if (result) {
      searchForm.value.reports = result.gptCustomerReports
      console.log('获取到报告列表，数量:', searchForm.value.reports?.length)

      if (isInitializing.value && route.query.selectedReportId) {
        searchForm.value.selectedReportId = route.query.selectedReportId
        changeReport();

      } else if (isInitializing.value && searchForm.value.reports?.length > 0) {
        console.log('初始化状态，自动选择第一个报告:', searchForm.value.reports[0])
        searchForm.value.selectedReportId = searchForm.value.reports[0].gptCustomerReportID
        console.log('设置selectedReportId:', searchForm.value.selectedReportId)
        changeReport()
      }
    }
  } catch (error) {
    ElMessage.error('查询报告失败')
    console.error('搜索报告失败:', error)
  }
}

const changeReport = () => {
  console.log('changeReport被调用，selectedReportId:', searchForm.value.selectedReportId)
  searchForm.value.selectedReport = searchForm.value.reports.find((item: any) => item.gptCustomerReportID === searchForm.value.selectedReportId)
  console.log('找到选中的报告:', searchForm.value.selectedReport)
  searchForm.value.period = undefined
  searchForm.value.name = undefined
  searchDpCustomerReportShot()
}

const searchDpCustomerReportShot = async () => {
  console.log('开始搜索报告快照，isInitializing:', isInitializing.value)
  searchForm.value.shots = []
  searchForm.value.periodList = []
  searchForm.value.nameList = []
  searchForm.value.snapshot = {}

  try {
    const result = await visualApi.searchDpCustomerReportShot(
      searchForm.value.selectedReport.gptCustomerReportID
    )

    console.log('报告快照搜索结果:', result)
    if (result) {
      const onlineShotList = result.gptCustomerReportShots
      console.log('获取到快照列表，数量:', onlineShotList?.length)

      if (hasUserConfig(onlineShotList)) {
        const currentUserName = storage.local.get('username')
        console.log('检测到用户配置，当前用户:', currentUserName)
        onlineShotList.forEach((shot: any) => {
          if (shot.relatedUsers.split(',').includes(currentUserName)) {
            searchForm.value.shots.push(shot)
          }
        })
      } else {
        console.log('无用户配置，使用所有快照')
        searchForm.value.shots = onlineShotList
      }

      console.log('筛选后的快照数量:', searchForm.value.shots.length)
      if (searchForm.value.shots.length > 0) {
        searchForm.value.shots.forEach((shot: any) => {
          const period = shot.dropDownName
          const name = shot.nameDropDownName

          if (!searchForm.value.periodList.includes(period) && period !== '') {
            searchForm.value.periodList.push(period)
          }
          if (!searchForm.value.nameList.includes(name) && name !== '') {
            searchForm.value.nameList.push(name)
          }
        })
      }

      console.log('生成的时间周期列表:', searchForm.value.periodList)
      console.log('生成的名称列表:', searchForm.value.nameList)

      if (isInitializing.value && route.query.selectedReportId) {
        searchForm.value.period = route.query.dropDownName
        if (searchForm.value.selectedReport.showNameDropdown === 'Y') {
          searchForm.value.name = route.query.nameDropDownName
        }
        markSnapshot()
        await loadVisuals()
        isInitializing.value = false

      } else if (isInitializing.value && searchForm.value.periodList?.length > 0) {
        console.log('初始化状态，自动选择第一个时间周期:', searchForm.value.periodList[0])
        searchForm.value.period = searchForm.value.periodList[0]
        if (searchForm.value.selectedReport.showNameDropdown === 'Y') {
          searchForm.value.name = searchForm.value.nameList[0]
        }
        console.log('调用markSnapshot')
        markSnapshot()
        console.log('调用loadVisuals')
        await loadVisuals()
        console.log('设置初始化状态为false')
        isInitializing.value = false
      }
    }
  } catch (error) {
    ElMessage.error('查询报告快照失败')
    console.error('搜索报告快照失败:', error)
  }
}

const hasUserConfig = (shotList: any[]) => {
  return shotList.some(shot =>
    shot.relatedUsers !== undefined && shot.relatedUsers.length > 0
  )
}

const markSnapshot = () => {
  console.log('markSnapshot被调用，当前period:', searchForm.value.period, '当前name:', searchForm.value.name)
  const withName = searchForm.value.name && searchForm.value.name !== ''
  let list: any[]

  if (withName) {
    console.log('使用名称筛选快照')
    list = searchForm.value.shots.filter((shot: any) =>
      searchForm.value.period === shot.dropDownName && searchForm.value.name === shot.nameDropDownName
    )
  } else {
    console.log('仅使用时间周期筛选快照')
    list = searchForm.value.shots.filter((shot: any) =>
      searchForm.value.period === shot.dropDownName
    )
  }

  console.log('筛选出的快照列表:', list)
  searchForm.value.snapshot = list.length > 0 ? list[0] : {}
  console.log('设置的快照:', searchForm.value.snapshot)
}

const loadVisuals = async () => {
  console.log('loadVisuals被调用')
  visualList.value = []
  filterVisualList.value = []
  activePage.value = markRaw(defineComponent({render: () => h(Empty)}))
  selectedRowKey.value = ''

  const snapshot = searchForm.value.snapshot
  console.log('当前快照:', snapshot)
  if (!snapshot || !snapshot.gptShotId) {
    console.log('快照无效或缺少gptShotId，退出loadVisuals')
    return
  }

  try {
    console.log('开始加载可视化列表')
    isLoading.value = true

    const result = await visualApi.findReportVisualsBy3Id(
      searchForm.value.selectedReport.gptStencilId,
      snapshot.gptShotId,
      vertexId.value
    )

    console.log('可视化查询结果:', result)
    if (!result || result.length === 0) {
      console.log('没有查询到报告页面')
      ElMessage.warning('没有查询到报告页面')
      return
    }

    visualList.value = result
    filterVisualList.value = result
    console.log('设置可视化列表，数量:', result.length)

    // 如果是初始化状态且有数据，自动点击第一个单元格
    if (isInitializing.value && result.length > 0) {
      console.log('自动点击第一个可视化项:', result[0])
      selectedRowKey.value = result[0].id
      currentVisual.value = result[0]
      // 延迟执行loadVisual，确保DOM已更新
      setTimeout(async () => {
        await loadVisual()
      }, 100)
    }

    await findPPT()

  } catch (error) {
    ElMessage.error('查询报告页面失败')
    console.error('loadVisuals失败:', error)
  } finally {
    isLoading.value = false
    console.log('loadVisuals完成')
  }
}

const loadSpecificVisuals = async () => {
  visualList.value = []
  filterVisualList.value = []
  activePage.value = markRaw(defineComponent({render: () => h(Empty)}))
  selectedRowKey.value = ''

  try {
    isLoading.value = true

    const result = await visualApi.findReportVisualsBy3Id(
      props.params.gptStencilId,
      props.params.gptShotId,
      vertexId.value
    )

    if (!result || result.length === 0) {
      ElMessage.warning('没有查询到报告页面')
      return
    }

    visualList.value = result
    filterVisualList.value = result

  } catch (error) {
    ElMessage.error('查询报告页面失败')
    console.error(error)
  } finally {
    isLoading.value = false
  }
}

const pickVisual = () => {
  activePage.value = markRaw(defineComponent({render: () => h(Empty)}))
  selectedRowKey.value = ''

  if (!searchForm.value.visualName || searchForm.value.visualName === '') {
    filterVisualList.value = [...visualList.value]
    return
  }

  filterVisualList.value = visualList.value.filter(item =>
    item.officialName.includes(searchForm.value.visualName)
  )
}

const changeVisual = (record: any, index: number) => ({
  onClick: () => {
    selectedRowKey.value = record.id
    currentVisual.value = record

    if (!currentVisual.value) {
      return
    }

    loadVisual()
  },
  style: {
    backgroundColor: record.id === selectedRowKey.value ? 'rgb(240,240,255)' : 'transparent'
  }
})

const loadVisual = async () => {
  console.log('loadVisual被调用，当前选中的可视化:', currentVisual.value)
  try {
    const visualJson = await visualApi.getGptVisual(
      currentVisual.value.stencilId,
      currentVisual.value.shotId,
      currentVisual.value.vertexId,
      currentVisual.value.indexId,
      currentVisual.value.visualStencilId,
      currentVisual.value.dataPeriod
    )

    console.log('获取到的可视化数据:', visualJson)
    if (!visualJson) {
      console.log('没有查询到可视化数据')
      ElMessage.error('没有查询到可视化')
      return
    }

    visualData.value = JSON.stringify(visualJson)
    activePage.value = markRaw(defineComponent({render: () => h(SubjectVisual)}))
    console.log('可视化组件已加载')

  } catch (error) {
    ElMessage.error('查询报告页面失败')
    console.error('loadVisual失败:', error)
  }
}

const findPPT = async () => {
  pptPath.value = ''
  const pptStatus = searchForm.value.snapshot.pptStatus

  if (pptStatus === 'offline') {
    pptExists.value = false
    return
  }

  try {
    const result = await visualApi.findGptReport(
      searchForm.value.selectedReport.gptStencilId,
      searchForm.value.snapshot.gptShotId,
      vertexId.value
    )

    if (result) {
      const gptReports = result.gptReports
      if (gptReports.length > 0) {
        pptPath.value = gptReports[0].pptPath
      }
    }

    pptExists.value = pptPath.value !== undefined && pptPath.value.length > 0

  } catch (error) {
    ElMessage.error('查询PPT失败')
    console.error(error)
  }
}

const download = () => {
  Modal.confirm({
    title: '提示',
    content: '下载PPT文件?',
    okText: '确定',
    cancelText: '取消',
    async onOk() {
      try {
        const result = await visualApi.downloadDpPPT(pptPath.value);
        const byteString = atob(result); // Base64解码
        const bytes = new Uint8Array(byteString.length);
        for (let i = 0; i < byteString.length; i++) {
          bytes[i] = byteString.charCodeAt(i);
        }
        const blob = new Blob([bytes], {type: 'application/octet-stream'});
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = pptPath.value.split('/').pop() || '.pptx';
        a.style.display = 'none';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
      } catch (error) {
        ElMessage.error('下载失败');
        console.error('下载失败:', error);
      }
    },
    onCancel() {
      ElMessage.info('已取消')
    }
  })
}

</script>

<template>
  <div v-loading="isLoading">
    <a-card v-if="!isSpecific" :bordered="false" :style="{ margin: '15px' }">
      <a-form layout="inline" :model="searchForm">
        <a-form-item style="margin-bottom: 0;" label="报告">
          <a-select
            v-model:value="searchForm.selectedReportId"
            placeholder="报告"
            @change="changeReport"
            style="width: 250px;"
          >
            <a-select-option
              v-for="item in searchForm.reports"
              :key="item.gptCustomerReportID"
              :value="item.gptCustomerReportID"
            >
              {{ item.menuName }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item style="margin-bottom: 0;" label="时间周期">
          <a-select
            v-model:value="searchForm.period"
            placeholder="时间周期"
            @change="markSnapshot"
            style="width: 200px;"
          >
            <a-select-option
              v-for="item in searchForm.periodList"
              :key="item"
              :value="item"
            >
              {{ item }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item v-if="searchForm.selectedReport?.showNameDropdown === 'Y'" style="margin-bottom: 0;" label="名称">
          <a-select
            v-model:value="searchForm.name"
            placeholder="名称"
            @change="markSnapshot"
            style="width: 200px;"
          >
            <a-select-option
              v-for="item in searchForm.nameList"
              :key="item"
              :value="item"
            >
              {{ item }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item style="margin-bottom: 2px;">
          <a-button class="mainButton" type="primary" @click="loadVisuals">查看</a-button>
        </a-form-item>
<!--        <a-form-item style="margin-bottom: 2px;">-->
<!--          <a-button class="mainButton" type="primary" @click="navigateToAnalysisReport">分析报告</a-button>-->
<!--        </a-form-item>-->
        <a-form-item style="margin-bottom: 2px;">
          <a-input style="width: 200px;" v-model:value="searchForm.visualName" placeholder="页面名称"></a-input>
        </a-form-item>
        <a-form-item style="margin-bottom: 2px;">
          <a-button @click="pickVisual">过滤目录</a-button>
        </a-form-item>
        <a-form-item v-show="pptExists" style="margin-bottom: 2px;">
          <a-button class="mainButton" type="primary" @click="download">下载PPT</a-button>
        </a-form-item>
      </a-form>
    </a-card>
    <a-row :style="{ margin: '15px' }">
      <a-col :span="4" :style="{ padding:'0 10px 0 0',height:'702px',overflowY: 'scroll' }">
        <a-card class="tableCard">
          <a-table
            class="antTableClass"
            :columns="columns"
            :dataSource="filterVisualList"
            :pagination="false"
            :showHeader="false"
            :customRow="changeVisual"
            :style="{ width: '100%', cursor: 'pointer' }"
            :scroll="{ y: 690 }"
          />
        </a-card>
      </a-col>
      <a-col :span="20" :style="{height:'702px'}">
        <a-card class="tableCard">
          <component ref="activeZone" :is="activePage" :visualData="visualData" :style="{padding: '30px 20px'}"/>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<style scoped>
.tableCard :deep(.ant-card-body) {
  height: 700px;
  padding: 5px;
  background: white;
  border-radius: 8px;
  box-shadow: 4px 4px 8px rgb(0 0 0 / 10%);
}

.antTableClass :deep(.ant-table-cell) {
  padding: 12px 5px;
}

.mainButton {
  background-color: rgb(103 22 232);
  border-color: rgb(103 22 232);
}

.mainButton:hover {
  background-color: rgb(153 72 255);
  border-color: rgb(153 72 255);
}
</style>

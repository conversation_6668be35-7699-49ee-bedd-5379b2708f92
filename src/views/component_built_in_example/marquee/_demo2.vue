<script lang="ts" setup>
const reviews = [
  { name: 'c***@msn.com', content: '从较低的上手难度，到完善的细节设计；从完善的示例，到详细而循序渐进的文档。无不体现作者作为一个技术专家之上的产品意识，这是诸多“开发框架”所达不到的理念高度。' },
  { name: 't***@126.com', content: '售后服务及时，耐心解答各种问题，源码讲解耐心，努力帮助客户了解系统架构，从而更好的服务客服体系，界面美观，集成快捷，非常棒的框架。' },
  { name: 'l***@163.com', content: '可以说是目前最简单、易上手的 Vue3 中后台框架，框架本身已经涵盖了开发中几乎所有的基础功能，可以让开发者更注重业务本身的开发。' },
  { name: '3***@qq.com', content: '无论从颜值、组件丰富程度来说都是非常优秀的，对后端开发十分友好，代码规范清晰，即便不是很熟悉前端的后端开发人员也能快速上手。' },
  { name: 't***@163.com', content: 'Fantastic-admin 的设计、交互都十分现代化，文档健全，售后及时，最重要的是，它几乎没有什么 Bug ，使用体验太丝滑了。' },
  { name: 'y***@163.com', content: '作为一个16年开发经验的老程序员，一看到这套框架就被深深的吸引，在对比开发成本和购买成本后，果断选择了专业版。' },
  { name: '4***@qq.com', content: '做为个人开发者，本人兼任前端和后端的工作。Fantastic-admin 拿来即用，解决了没有前端，没有美工的困扰。' },
  { name: 'b***@gmail.com', content: '我觉得 Fantastic-admin 最大的优势和吸引人的地方是产品性很强，前两年就买了专业版，一直在用。' },
  { name: 'r***@aliyun.com', content: '我是搞后端的，这个模板好好用，期待加入更多的页面可以直接用。' },
  { name: 'a***@163.com', content: '厉害👍🏻，是后台系统里见过最好的框架。' },
]
</script>

<template>
  <FaMarquee reverse>
    <figure v-for="review in reviews" :key="review.name" class="w-64 cursor-pointer overflow-hidden border border-gray-950/[.1] rounded-xl bg-gray-950/[.01] p-4 dark:border-gray-50/[.1] dark:bg-gray-50/[.10] hover:bg-gray-950/[.05] dark:hover:bg-gray-50/[.15]">
      <div class="flex flex-row items-center gap-2">
        <FaAvatar :src="`https://avatar.vercel.sh/${review.name}`" class="size-8" />
        <div class="flex flex-col">
          <span class="text-sm font-medium dark:text-white">
            {{ review.name }}
          </span>
        </div>
      </div>
      <blockquote class="mt-2 text-pretty text-sm">
        {{ review.content }}
      </blockquote>
    </figure>
  </FaMarquee>
</template>

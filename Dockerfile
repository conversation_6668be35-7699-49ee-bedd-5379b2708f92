# Use a specific Java runtime version (Java 23 JRE based on Ubuntu Jammy)
# Check OpenJDK page on Docker Hub for available tags: https://hub.docker.com/_/openjdk
FROM openjdk:23-oraclelinux7

# Set working directory
WORKDIR /app

# Copy the executable jar file built by <PERSON><PERSON> (adjust the path if necessary)
# Assumes the JAR file is in the 'target' directory and follows Spring Boot's naming convention
COPY target/dp-server-*.jar app.jar

# Expose the port the application runs on (default for Spring Boot is 8080)
EXPOSE 8080

# Command to run the application
ENTRYPOINT ["java", "-jar", "app.jar"] 
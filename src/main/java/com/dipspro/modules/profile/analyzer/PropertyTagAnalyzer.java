package com.dipspro.modules.profile.analyzer;

import com.dipspro.modules.profile.dto.ProjectDetail;
import com.dipspro.modules.profile.dto.UserProfileTag;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.dipspro.constant.ProjectStatsConstants.PREFERRED_CATEGORY_THRESHOLD;
import static com.dipspro.constant.ProjectStatsConstants.TAG_TYPE_LIVING_PREFERENCE;

/**
 * 房产特性标签分析器 - 处理与房屋特性相关的标签，如装修类型、面积、房型等
 * 
 * <AUTHOR>
 */
public class PropertyTagAnalyzer extends UserProfileAnalyzer {
    
    // 装修类型标签代码
    private static final String TAG_DECORATED_PREFERENCE = "decorated_preference";
    private static final String TAG_ROUGH_PREFERENCE = "rough_preference";
    
    // 房型(建筑类型)标签代码
    private static final String TAG_LOWRISE_PREFERENCE = "lowrise_preference";
    private static final String TAG_HIGHRISE_PREFERENCE = "highrise_preference";
    private static final String TAG_GARDEN_HOUSE_PREFERENCE = "garden_house_preference";
    private static final String TAG_VILLA_PREFERENCE = "villa_preference";
    private static final String TAG_APARTMENT_PREFERENCE = "apartment_preference";
    
    /**
     * 构造函数
     * 
     * @param projects 项目列表
     * @param userProfileTags 用户画像标签列表
     */
    public PropertyTagAnalyzer(List<ProjectDetail> projects, List<UserProfileTag> userProfileTags) {
        super(projects, userProfileTags);
    }
    
    @Override
    protected void doAnalyze(StringRedisTemplate redisTemplate) {
        // 实现所有分析方法
        analyzeDecorationTypePreference();
        analyzeHouseTypePreference();
    }
    
    /**
     * 分析装修类型偏好标签
     * 标签规则：namelist中超过一半的房产为精装修/毛坯
     */
    public void analyzeDecorationTypePreference() {
        if (projects.size() < 2) {
            return;
        }
        
        int decoratedCount = 0;
        int roughCount = 0;
        int totalWithDecorationInfo = 0;
        
        List<ProjectDetail> decoratedProjects = new ArrayList<>();
        List<ProjectDetail> roughProjects = new ArrayList<>();
        
        for (ProjectDetail project : projects) {
            if (StringUtils.isNotBlank(project.getDecorationType())) {
                String decorationType = project.getDecorationType().toLowerCase();
                totalWithDecorationInfo++;
                
                if (decorationType.contains("精装") || decorationType.contains("豪装") || 
                    decorationType.contains("装修") || decorationType.contains("中装") ||
                    decorationType.contains("简装")) {
                    decoratedCount++;
                    decoratedProjects.add(project);
                } else if (decorationType.contains("毛坯")) {
                    roughCount++;
                    roughProjects.add(project);
                }
            }
        }
        
        // 分析精装修偏好
        if (totalWithDecorationInfo > 0 && 
            (double)decoratedCount / totalWithDecorationInfo >= PREFERRED_CATEGORY_THRESHOLD && 
            decoratedCount >= 2) {
            
            UserProfileTag tag = UserProfileTag.create(
                TAG_DECORATED_PREFERENCE,
                "精装修偏好", 
                "客户偏好购买精装修房产", 
                TAG_TYPE_LIVING_PREFERENCE
            );
            
            // 添加匹配项目
            for (ProjectDetail project : decoratedProjects) {
                if (StringUtils.isNotBlank(project.getCustomerProject()) && 
                    StringUtils.isNotBlank(project.getProjectName())) {
                    tag.addMatchedProject(project.getCustomerProject(), project.getProjectName());
                }
            }
            
            addTag(tag);
        }
        
        // 分析毛坯偏好
        if (totalWithDecorationInfo > 0 && 
            (double)roughCount / totalWithDecorationInfo >= PREFERRED_CATEGORY_THRESHOLD && 
            roughCount >= 2) {
            
            UserProfileTag tag = UserProfileTag.create(
                TAG_ROUGH_PREFERENCE,
                "毛坯房偏好", 
                "客户偏好购买毛坯房产", 
                TAG_TYPE_LIVING_PREFERENCE
            );
            
            // 添加匹配项目
            for (ProjectDetail project : roughProjects) {
                if (StringUtils.isNotBlank(project.getCustomerProject()) && 
                    StringUtils.isNotBlank(project.getProjectName())) {
                    tag.addMatchedProject(project.getCustomerProject(), project.getProjectName());
                }
            }
            
            addTag(tag);
        }
    }
    
    /**
     * 分析房型偏好标签（建筑类型）
     * 标签规则：namelist中超过一半的房产为同一种房型
     * 房型可选项：小高层、高层、多层/花园洋房、别墅、酒店式公寓/SOHO公寓、其他
     */
    public void analyzeHouseTypePreference() {
        if (projects.isEmpty()) {
            return;
        }
        
        Map<String, List<ProjectDetail>> houseTypeMap = new HashMap<>();
        int totalWithHouseTypeInfo = 0;
        
        // 标准化处理的房型类别
        Map<String, String> normalizedTypes = new HashMap<>();
        normalizedTypes.put("小高层", TAG_LOWRISE_PREFERENCE);
        normalizedTypes.put("高层", TAG_HIGHRISE_PREFERENCE);
        normalizedTypes.put("多层/花园洋房", TAG_GARDEN_HOUSE_PREFERENCE);
        normalizedTypes.put("别墅", TAG_VILLA_PREFERENCE);
        normalizedTypes.put("酒店式公寓/SOHO公寓", TAG_APARTMENT_PREFERENCE);
        
        for (ProjectDetail project : projects) {
            if (StringUtils.isNotBlank(project.getHouseType())) {
                String houseType = project.getHouseType().trim();
                totalWithHouseTypeInfo++;
                
                // 标准化房型分类
                String normalizedType = null;
                for (Map.Entry<String, String> entry : normalizedTypes.entrySet()) {
                    if (houseType.contains(entry.getKey())) {
                        normalizedType = entry.getKey();
                        break;
                    }
                }
                
                // 如果没匹配到标准类型，归为"其他"
                if (normalizedType == null) {
                    normalizedType = "其他";
                }
                
                if (!houseTypeMap.containsKey(normalizedType)) {
                    houseTypeMap.put(normalizedType, new ArrayList<>());
                }
                houseTypeMap.get(normalizedType).add(project);
            }
        }
        
        // 检查是否有某种房型占比超过阈值
        for (Map.Entry<String, List<ProjectDetail>> entry : houseTypeMap.entrySet()) {
            String houseType = entry.getKey();
            List<ProjectDetail> projectsWithType = entry.getValue();
            
            if (totalWithHouseTypeInfo > 0 && 
                (double)projectsWithType.size() / totalWithHouseTypeInfo >= PREFERRED_CATEGORY_THRESHOLD && 
                projectsWithType.size() >= 2) {
                
                // 确定标签代码
                String tagCode;
                if (normalizedTypes.containsKey(houseType)) {
                    tagCode = normalizedTypes.get(houseType);
                } else {
                    tagCode = "house_type_other";
                }
                
                UserProfileTag tag = UserProfileTag.create(
                    tagCode,
                    houseType + "偏好", 
                    "客户偏好购买" + houseType + "类型的房产", 
                    TAG_TYPE_LIVING_PREFERENCE
                );
                
                // 添加匹配项目
                for (ProjectDetail project : projectsWithType) {
                    if (StringUtils.isNotBlank(project.getCustomerProject()) && 
                        StringUtils.isNotBlank(project.getProjectName())) {
                        tag.addMatchedProject(project.getCustomerProject(), project.getProjectName());
                    }
                }
                
                addTag(tag);
            }
        }
    }
    
}
import type { RouteRecordRaw } from 'vue-router'
import { $t } from '@/locales'

function Layout() {
  return import('@/layouts/index.vue')
}

const routes: RouteRecordRaw = {
  path: '/standard_module_example',
  component: Layout,
  name: 'standardModuleExample',
  meta: {
    title: $t('route.standardModule.root'),
    icon: 'i-ic:round-view-module',
    badge: 'PRO',
  },
  children: [
    {
      path: '',
      name: 'standardModuleExampleList',
      component: () => import('@/views/standard_module_example/list.vue'),
      meta: {
        title: $t('route.standardModule.list'),
        menu: false,
        breadcrumb: false,
        cache: ['standardModuleExampleCreate', 'standardModuleExampleEdit'],
      },
    },
    {
      path: 'detail',
      name: 'standardModuleExampleCreate',
      component: () => import('@/views/standard_module_example/detail.vue'),
      meta: {
        title: $t('route.standardModule.create'),
        menu: false,
        activeMenu: '/standard_module_example',
        cache: true,
        noCache: 'standardModuleExampleList',
      },
    },
    {
      path: 'detail/:id',
      name: 'standardModuleExampleEdit',
      component: () => import('@/views/standard_module_example/detail.vue'),
      meta: {
        title: $t('route.standardModule.edit'),
        menu: false,
        activeMenu: '/standard_module_example',
        cache: true,
        noCache: 'standardModuleExampleList',
      },
    },
  ],
}

export default routes

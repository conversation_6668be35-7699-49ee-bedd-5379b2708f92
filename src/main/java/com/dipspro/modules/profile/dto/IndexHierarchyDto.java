package com.dipspro.modules.profile.dto;

import java.util.List;

import com.dipspro.modules.chat.entity.IndexDefinition;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 指标层次结构DTO
 * 用于展示主指标与其子指标的层级关系
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IndexHierarchyDto {
    
    /**
     * 主指标定义
     */
    private IndexDefinition mainIndex;
    
    /**
     * 子指标列表
     */
    private List<IndexDefinition> subIndexes;
    
    /**
     * 子指标数量
     */
    private int subIndexCount;
} 
# Embedding 模块文档

## 概述

Embedding 模块是基于 `shibing624/text2vec-base-chinese` 模型的中文句子转向量服务，使用 ONNX Runtime 进行推理，支持线程安全的多线程调用。该模块采用 Session Pool 模式确保线程安全，提供高性能的文本向量化和相似度计算功能。

## 模块架构

```
com.dipspro.modules.embedding/
├── controller/
│   └── EmbeddingController.java          # REST API 控制器
├── dto/
│   ├── EmbeddingRequest.java             # 向量化请求 DTO
│   ├── EmbeddingResponse.java            # 向量化响应 DTO
│   ├── SimilarityRequest.java            # 相似度计算请求 DTO
│   └── SimilarityResponse.java           # 相似度计算响应 DTO
└── service/
    ├── EmbeddingService.java             # 服务接口
    └── impl/
        └── Text2VecEmbeddingService.java # 服务实现类
```

## 核心功能

### 1. 文本向量化
- **单文本编码**: 将单个中文句子转换为 768 维向量
- **批量编码**: 支持批量处理多个文本，提供真正的批量推理优化
- **异步处理**: 支持异步编码，适用于大量数据处理
- **智能分批**: 根据数据量自动选择最优处理策略

### 2. 相似度计算
- **余弦相似度**: 计算两个向量之间的余弦相似度 [-1, 1]
- **相似度矩阵**: 支持计算多个文本之间的相似度矩阵
- **向量直接计算**: 支持直接使用向量计算相似度，无需重复编码

### 3. 线程安全设计
- **Session Pool**: 使用会话池模式，每个线程独立使用 Session 实例
- **线程安全词汇表**: 使用 ConcurrentHashMap 存储词汇表
- **优化的批量处理**: 支持并发批量处理，提高吞吐量

## 技术特性

### 模型规格
- **模型**: shibing624/text2vec-base-chinese
- **向量维度**: 768
- **最大序列长度**: 512
- **支持语言**: 中文

### 性能优化
- **Session Pool**: 预创建多个 ONNX Session 实例，避免重复初始化开销
- **批量推理**: 真正的批量推理，而非简单的并行单条推理
- **智能分批**: 根据数据量和线程池大小动态调整批次大小
- **内存优化**: 及时释放张量资源，避免内存泄漏

### 容错机制
- **降级模式**: 模型文件或词汇表缺失时，服务仍能启动但返回零向量
- **错误日志**: 详细的错误日志记录，便于问题排查
- **健康检查**: 提供服务状态检查接口

## 配置说明

### application.yml 配置

```yaml
embedding:
  model:
    path: "models/text2vec-base-chinese.onnx"  # ONNX 模型文件路径
    vocab-path: "models/vocab.txt"              # 词汇表文件路径
  thread-pool:
    size: 4                                     # 线程池大小
```

### 配置参数说明

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `embedding.model.path` | `models/text2vec-base-chinese.onnx` | ONNX 模型文件路径 |
| `embedding.model.vocab-path` | `models/vocab.txt` | 词汇表文件路径 |
| `embedding.thread-pool.size` | `4` | 线程池大小，建议设置为 CPU 核心数 |

## API 接口

### 1. 健康检查

```http
GET /api/embedding/health
```

**响应示例**:
```json
{
  "success": true,
  "data": "服务正常运行",
  "message": "操作成功"
}
```

### 2. 文本向量化

```http
POST /api/embedding/encode
Content-Type: application/json
```

**请求参数**:
```json
{
  "text": "北京万科广场",              // 单文本（与 texts 二选一）
  "texts": ["文本1", "文本2"],        // 批量文本（与 text 二选一）
  "async": false,                    // 是否异步处理
  "includeSimilarity": false         // 是否计算相似度矩阵（仅批量时有效）
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "vector": [0.1, 0.2, ...],        // 单文本向量
    "vectors": [[0.1, 0.2, ...], ...], // 批量向量
    "dimension": 768,
    "textCount": 1,
    "processingTimeMs": 45,
    "similarityMatrix": [[1.0, 0.8], [0.8, 1.0]], // 相似度矩阵（可选）
    "success": true
  }
}
```

### 3. 相似度计算

```http
POST /api/embedding/similarity
Content-Type: application/json
```

**请求参数**:
```json
{
  "text1": "北京万科广场",
  "text2": "万科广场项目",
  "vector1": [0.1, 0.2, ...],        // 可选，直接提供向量
  "vector2": [0.3, 0.4, ...],        // 可选，直接提供向量
  "async": false
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "similarity": 0.85,
    "vector1": [0.1, 0.2, ...],
    "vector2": [0.3, 0.4, ...],
    "dimension": 768,
    "processingTimeMs": 32,
    "success": true
  }
}
```

## 使用示例

### Java 代码示例

```java
@Autowired
private EmbeddingService embeddingService;

// 单文本编码
float[] vector = embeddingService.encode("北京万科广场");

// 批量编码
List<String> texts = Arrays.asList("文本1", "文本2", "文本3");
List<float[]> vectors = embeddingService.encodeBatch(texts);

// 异步编码
CompletableFuture<float[]> future = embeddingService.encodeAsync("异步处理文本");
float[] result = future.get();

// 相似度计算
double similarity = embeddingService.cosineSimilarity(vector1, vector2);

// 检查服务状态
boolean initialized = embeddingService.isInitialized();
```

### cURL 示例

```bash
# 单文本编码
curl -X POST http://localhost:8080/api/embedding/encode \
  -H "Content-Type: application/json" \
  -d '{"text": "北京万科广场"}'

# 批量编码
curl -X POST http://localhost:8080/api/embedding/encode \
  -H "Content-Type: application/json" \
  -d '{"texts": ["楼盘档案：北京万科广场", "楼盘画像：北京万科广场"], "includeSimilarity": true}'

# 相似度计算
curl -X POST http://localhost:8080/api/embedding/similarity \
  -H "Content-Type: application/json" \
  -d '{"text1": "北京万科广场", "text2": "万科广场项目"}'
```

## 性能指标

### 处理能力
- **单文本编码**: ~50ms（包含分词和推理）
- **批量编码**: 32条文本 ~200ms（真正批量推理）
- **大批量处理**: 自动分批，支持万级数据处理
- **并发能力**: 支持多线程并发调用

### 内存使用
- **模型加载**: ~500MB（ONNX 模型）
- **词汇表**: ~10MB（21128 个词汇）
- **Session Pool**: 每个 Session ~50MB
- **运行时**: 动态分配，及时释放

## 部署要求

### 系统要求
- **Java**: JDK 17+
- **内存**: 建议 2GB+
- **CPU**: 支持多核并行处理
- **存储**: 模型文件 ~500MB

### 依赖项
```xml
<dependency>
    <groupId>com.microsoft.onnxruntime</groupId>
    <artifactId>onnxruntime</artifactId>
    <version>1.15.1</version>
</dependency>
```

### 模型文件
需要准备以下文件：
- `text2vec-base-chinese.onnx`: ONNX 格式的模型文件
- `vocab.txt`: 词汇表文件（每行一个词汇，共 21128 行）

## 故障排除

### 常见问题

1. **服务启动失败**
   - 检查模型文件路径是否正确
   - 确认词汇表文件是否存在
   - 查看日志中的详细错误信息

2. **内存不足**
   - 减少线程池大小
   - 调整 JVM 堆内存设置
   - 检查是否有内存泄漏

3. **性能问题**
   - 调整线程池大小
   - 使用批量接口而非单条接口
   - 考虑使用异步处理

### 日志级别
```yaml
logging:
  level:
    com.dipspro.modules.embedding: DEBUG
```

## 扩展开发

### 自定义实现
可以通过实现 `EmbeddingService` 接口来提供自定义的向量化服务：

```java
@Service
@Primary
public class CustomEmbeddingService implements EmbeddingService {
    // 自定义实现
}
```

### 模型替换
支持替换为其他 ONNX 格式的文本向量化模型，需要：
1. 确保输入输出格式兼容
2. 调整相关常量（向量维度、序列长度等）
3. 更新词汇表文件

## 版本历史

- **v1.0.0**: 基础功能实现
- **v1.1.0**: 添加批量推理优化
- **v1.2.0**: 增加线程安全和 Session Pool
- **v1.3.0**: 支持降级模式和容错机制

## 许可证

本模块遵循项目整体许可证。模型 `shibing624/text2vec-base-chinese` 请参考其原始许可证。
package com.dipspro.modules.profile.analyzer;

import com.dipspro.modules.profile.dto.ProjectDetail;
import com.dipspro.modules.profile.dto.UserProfileTag;
import lombok.Getter;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.util.ArrayList;
import java.util.List;

/**
 * 用户画像标签分析器基类
 * 
 * <AUTHOR>
 */
public abstract class UserProfileAnalyzer {
    
    protected final List<ProjectDetail> projects;
    @Getter
    protected final List<UserProfileTag> userProfileTags;
    
    /**
     * 构造函数
     * 
     * @param projects 项目列表
     */
    public UserProfileAnalyzer(List<ProjectDetail> projects) {
        this.projects = projects;
        this.userProfileTags = new ArrayList<>();
    }
    
    /**
     * 构造函数 - 用于子类分析器
     * 
     * @param projects 项目列表
     * @param userProfileTags 标签列表（由主分析器传入，用于汇总标签）
     */
    protected UserProfileAnalyzer(List<ProjectDetail> projects, List<UserProfileTag> userProfileTags) {
        this.projects = projects;
        this.userProfileTags = userProfileTags;
    }
    
    /**
     * 执行所有标签分析
     */
    public void analyzeAllTags(StringRedisTemplate redisTemplate) {
        if (projects == null || projects.isEmpty()) {
            return;
        }
        
        // 清空现有标签，重新分析
        this.userProfileTags.clear();
        
        // 创建分析器子类实例
        createAnalyzers();
        
        // 执行具体分析逻辑
        doAnalyze(redisTemplate);
    }
    
    /**
     * 创建子分析器实例并执行分析
     * 主分析器需重写此方法
     */
    protected void createAnalyzers() {
        // 由具体的主分析器实现
    }
    
    /**
     * 执行具体分析逻辑
     * 子分析器需重写此方法
     */
    protected abstract void doAnalyze(StringRedisTemplate redisTemplate);

    /**
     * 标签是否存在
     * 
     * @param tagCode 标签代码
     * @return 是否存在
     */
    protected boolean hasTag(String tagCode) {
        return userProfileTags.stream()
            .anyMatch(tag -> tagCode.equals(tag.getCode()));
    }
    
    /**
     * 添加标签到列表
     * 
     * @param tag 标签对象
     */
    protected void addTag(UserProfileTag tag) {
        if (tag != null && !hasTag(tag.getCode())) {
            userProfileTags.add(tag);
        }
    }
} 
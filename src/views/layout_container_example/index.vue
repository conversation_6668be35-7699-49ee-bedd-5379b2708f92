<route lang="yaml">
meta:
  enabled: false
</route>

<script setup lang="ts">
const router = useRouter()

const vertical = ref(false)
const enableLeftSide = ref(true)
const enableRightSide = ref(true)
const enableTopSide = ref(true)
const enableBottomSide = ref(true)
const leftSideWidth = ref(300)
const rightSideWidth = ref(300)
const topSideHeight = ref(200)
const bottomSideHeight = ref(200)
</script>

<template>
  <div class="absolute size-full flex flex-col">
    <FaPageHeader title="布局容器" class="mb-0" />
    <div class="relative flex-1">
      <FaLayoutContainer
        :vertical="vertical"
        :enable-left-side="enableLeftSide"
        :enable-right-side="enableRightSide"
        :enable-top-side="enableTopSide"
        :enable-bottom-side="enableBottomSide"
        :left-side-width="leftSideWidth"
        :right-side-width="rightSideWidth"
        :top-side-height="topSideHeight"
        :bottom-side-height="bottomSideHeight"
      >
        <template #leftSide>
          <div class="space-y-2">
            <h2>左侧栏</h2>
            <hr>
            <p>调整宽度</p>
            <ElSlider v-model="leftSideWidth" :step="10" :min="250" :max="350" />
          </div>
        </template>
        <template #rightSide>
          <div class="space-y-2">
            <h2>右侧栏</h2>
            <hr>
            <p>调整宽度</p>
            <ElSlider v-model="rightSideWidth" :step="10" :min="250" :max="350" />
          </div>
        </template>
        <template #topSide>
          <div class="space-y-2">
            <h2>顶部栏</h2>
            <hr>
            <p>调整高度</p>
            <ElSlider v-model="topSideHeight" :step="10" :min="150" :max="250" />
          </div>
        </template>
        <template #bottomSide>
          <div class="space-y-4">
            <h2>底部栏</h2>
            <hr>
            <p>调整高度</p>
            <ElSlider v-model="bottomSideHeight" :step="10" :min="150" :max="250" />
          </div>
        </template>
        <div class="space-y-4">
          <FaPageMain title="布局方向" class="m-0" main-class="flex-center-start gap-2">
            <span class="text-sm">水平</span>
            <FaSwitch v-model="vertical" />
            <span class="text-sm">垂直</span>
          </FaPageMain>
          <div class="grid grid-cols-4 gap-4">
            <FaPageMain title="左侧栏" class="m-0">
              <FaSwitch v-model="enableLeftSide" />
            </FaPageMain>
            <FaPageMain title="右侧栏" class="m-0">
              <FaSwitch v-model="enableRightSide" />
            </FaPageMain>
            <FaPageMain title="顶部栏" class="m-0">
              <FaSwitch v-model="enableTopSide" />
            </FaPageMain>
            <FaPageMain title="底部栏" class="m-0">
              <FaSwitch v-model="enableBottomSide" />
            </FaPageMain>
          </div>
          <FaButton @click="router.push({ name: 'pagesExampleSidelayout' })">
            查看示例页面
          </FaButton>
        </div>
      </FaLayoutContainer>
    </div>
  </div>
</template>

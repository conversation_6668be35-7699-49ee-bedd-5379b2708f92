package com.dipspro.modules.ym.controller;

import com.dipspro.common.dto.ApiResponse;
import com.dipspro.modules.ym.dto.*;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 医美数据控制器
 */
@RestController
@RequestMapping("/api/ym")
@Slf4j
public class YmController {

    @PostMapping("/org_list")
    public ApiResponse<List<WxOrgDto>> getOrgList(@RequestBody WxParam param) {
        log.info("获取机构列表，参数: {}", param);
        List<WxOrgDto> list = Lists.newArrayList();
        list.add(WxOrgDto.builder().id(1L).wxOpenId(param.getWx_open_id()).unionId(param.getUnionid()).orgId("org_id_1").orgName("艺星广州天河一店").build());
        list.add(WxOrgDto.builder().id(2L).wxOpenId(param.getWx_open_id()).unionId(param.getUnionid()).orgId("org_id_2").orgName("艺星广州黄埔一店").build());
        list.add(WxOrgDto.builder().id(3L).wxOpenId(param.getWx_open_id()).unionId(param.getUnionid()).orgId("org_id_3").orgName("艺星广州珠江新城店").build());
        return ApiResponse.success(list);
    }

    @PostMapping("/order_list")
    public ApiResponse<List<OrderDto>> getOrderList(@RequestBody WxParam param) {
        log.info("获取订单列表，参数：{}", param);
        List<OrderDto> list = Lists.newArrayList();

        ProductDto product1 = ProductDto.builder().id(1L).uuid("uuid_1").name("默认套餐").price(2999).build();
        ProductDto product2 = ProductDto.builder().id(1L).uuid("uuid_2").name("默认套餐").price(3999).build();
        ProductDto product3 = ProductDto.builder().id(1L).uuid("uuid_3").name("默认套餐").price(4999).build();

        list.add(OrderDto.builder().id(1L).wxOpenId(param.getWx_open_id()).unionId(param.getUnionid()).orgId("org_id_1").product(product1)
                .buyPrice(2999).startDate("2024-01-01").endDate("2024-08-31").build());
        list.add(OrderDto.builder().id(2L).wxOpenId(param.getWx_open_id()).unionId(param.getUnionid()).orgId("org_id_2").product(product2)
                .buyPrice(3999).startDate("2024-09-01").endDate("2025-02-28").build());
        list.add(OrderDto.builder().id(3L).wxOpenId(param.getWx_open_id()).unionId(param.getUnionid()).orgId("org_id_3").product(product3)
                .buyPrice(4999).startDate("2025-03-01").endDate("2025-07-31").build());
        return ApiResponse.success(list);
    }

    @PostMapping("/chartOption1")
    public ApiResponse<String> chartOption1(@RequestBody ChartParam param) {
        log.info("获取 eChart Option - 1，参数：{}", param);
        String option = "";
        return ApiResponse.success(option);
    }
}

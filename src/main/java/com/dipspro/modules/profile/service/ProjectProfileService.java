package com.dipspro.modules.profile.service;

import com.dipspro.modules.normalize.dto.ProjectProfileRequest;
import com.dipspro.modules.profile.dto.ProjectProfileDto;

/**
 * 楼盘画像服务接口
 * 
 * <AUTHOR>
 * @since 2025-06-03 14:51:45
 */
public interface ProjectProfileService {

    /**
     * 根据楼盘画像请求查询楼盘画像
     * 
     * @param request 楼盘画像请求参数
     * @return 楼盘画像信息
     */
    ProjectProfileDto queryProjectProfile(ProjectProfileRequest request);

    /**
     * 根据名称查询楼盘画像 - 获取楼盘的营销相关信息
     *
     * @param request 楼盘画像请求参数
     * @return 楼盘画像信息
     */
    ProjectProfileDto projectProfileSalesInfo(ProjectProfileRequest request);

    /**
     * 根据名称查询楼盘画像 - 了解楼盘周边直线距离 N 公里的其他楼盘情况
     *
     * @param request 楼盘画像请求参数
     * @return 楼盘画像信息
     */
    ProjectProfileDto projectProfileSurrounding(ProjectProfileRequest request);

    /**
     * 根据名称查询楼盘画像 - 了解楼盘时间范围、属性、指标得分
     *
     * @param request 楼盘画像请求参数
     * @return 楼盘画像信息
     */
    ProjectProfileDto projectProfileAna(ProjectProfileRequest request);
}
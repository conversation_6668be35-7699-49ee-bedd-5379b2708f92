<route lang="yaml">
meta:
  enabled: false
</route>

<script setup lang="ts">
import useSettingsStore from '@/store/modules/settings'
import useWatermarkStore from '@/store/modules/watermark'
import dayjs from 'dayjs'

const settingsStore = useSettingsStore()
const watermarkStore = useWatermarkStore()

const enableWatermark = computed({
  get() {
    return settingsStore.settings.app.enableWatermark
  },
  set(newValue) {
    settingsStore.$patch((state) => {
      state.settings.app.enableWatermark = newValue
    })
  },
})

function updateWatermark() {
  watermarkStore.update({
    content: dayjs().format('YYYY-MM-DD\nHH:mm:ss'),
  })
}
function resetWatermark() {
  watermarkStore.update()
}
</script>

<template>
  <div>
    <FaPageHeader title="页面水印" description="在某些场景下，不希望用户将系统里的信息随意截图并转发，这时可开启页面水印，以减少这种情况发生" />
    <FaPageMain title="可在 /src/store/modules/watermark.ts 文件里定制水印文案内容">
      <ElSpace>
        <ElRadioGroup v-model="enableWatermark">
          <ElRadioButton :value="true">
            开启
          </ElRadioButton>
          <ElRadioButton :value="false">
            关闭
          </ElRadioButton>
        </ElRadioGroup>
        <ElButton v-show="enableWatermark" @click="updateWatermark">
          更新水印
        </ElButton>
        <ElButton v-show="enableWatermark" @click="resetWatermark">
          重置水印
        </ElButton>
      </ElSpace>
    </FaPageMain>
  </div>
</template>

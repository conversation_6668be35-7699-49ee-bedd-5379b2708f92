package com.dipspro.modules.embedding.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 文本相似度计算响应DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class SimilarityResponse {

    /**
     * 余弦相似度值 [-1, 1]
     */
    private double similarity;

    /**
     * 第一个文本的向量（如果是从文本计算得出）
     */
    private float[] vector1;

    /**
     * 第二个文本的向量（如果是从文本计算得出）
     */
    private float[] vector2;

    /**
     * 向量维度
     */
    private int dimension;

    /**
     * 处理时间（毫秒）
     */
    private long processingTimeMs;

    /**
     * 是否异步处理
     */
    private boolean async;

    /**
     * 异步任务ID（仅在异步处理时返回）
     */
    private String taskId;

    /**
     * 错误信息
     */
    private String error;

    /**
     * 成功标识
     */
    private boolean success = true;

    /**
     * 相似度等级描述
     */
    private String similarityLevel;

    /**
     * 创建成功响应
     */
    public static SimilarityResponse success(double similarity, long processingTimeMs) {
        SimilarityResponse response = new SimilarityResponse();
        response.similarity = similarity;
        response.processingTimeMs = processingTimeMs;
        response.similarityLevel = getSimilarityLevel(similarity);
        response.success = true;
        return response;
    }

    /**
     * 创建带向量的成功响应
     */
    public static SimilarityResponse success(double similarity, float[] vector1, float[] vector2,
            long processingTimeMs) {
        SimilarityResponse response = new SimilarityResponse();
        response.similarity = similarity;
        response.vector1 = vector1;
        response.vector2 = vector2;
        response.dimension = vector1.length;
        response.processingTimeMs = processingTimeMs;
        response.similarityLevel = getSimilarityLevel(similarity);
        response.success = true;
        return response;
    }

    /**
     * 创建异步响应
     */
    public static SimilarityResponse async(String taskId) {
        SimilarityResponse response = new SimilarityResponse();
        response.async = true;
        response.taskId = taskId;
        response.success = true;
        return response;
    }

    /**
     * 创建错误响应
     */
    public static SimilarityResponse error(String error) {
        SimilarityResponse response = new SimilarityResponse();
        response.error = error;
        response.success = false;
        return response;
    }

    /**
     * 根据相似度值获取等级描述
     */
    private static String getSimilarityLevel(double similarity) {
        if (similarity >= 0.9) {
            return "极高相似";
        } else if (similarity >= 0.8) {
            return "高度相似";
        } else if (similarity >= 0.7) {
            return "较高相似";
        } else if (similarity >= 0.6) {
            return "中等相似";
        } else if (similarity >= 0.4) {
            return "较低相似";
        } else if (similarity >= 0.2) {
            return "低度相似";
        } else {
            return "几乎不相似";
        }
    }
}
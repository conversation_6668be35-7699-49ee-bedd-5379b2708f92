package com.dipspro.constant;

/**
 * 项目统计相关常量
 * 
 * <AUTHOR>
 */
public class ProjectStatsConstants {
    
    // 消息常量
    public static final String NO_PROJECT_INFO_MSG = "没有查询到客户历史购买物业信息。";
    public static final String PROJECT_LIST_HEADER = "#### 客户购买过以下物业：";
    public static final String PROPERTY_DETAILS_HEADER = "#### 物业详情：";
    public static final String NO_DETAIL_MATCH_MSG = "未匹配到楼盘详情。";
    public static final String USER_PROFILE_HEADER = "#### 用户画像标签：";
    
    // 格式化常量
    public static final String PRICE_FORMAT = "%.1f 万元";
    public static final double PRICE_TO_WAN_RATIO = 10000.0;
    public static final String FACILITY_SUFFIX = " 个";
    public static final String FACILITY_SEPARATOR = "，";
    
    // 标签类型常量
    public static final String TAG_TYPE_PURCHASE_BEHAVIOR = "购买行为";
    public static final String TAG_TYPE_CUSTOMER_VALUE = "客户价值";
    public static final String TAG_TYPE_BRAND_PREFERENCE = "品牌偏好";
    public static final String TAG_TYPE_LIVING_PREFERENCE = "居住偏好";
    public static final String TAG_TYPE_PURCHASE_DECISION = "购买决策因素";
    public static final String TAG_TYPE_CUSTOMER_TRAIT = "客户特征";
    
    // 标签代码常量
    public static final String TAG_HAS_PROPERTY_EXPERIENCE = "tag_has_property_experience"; // 置业经历
    public static final String TAG_MULTIPLE_PROPERTY = "multiple_property"; // 多次置业经历
    public static final String TAG_HIGH_NET_WORTH = "high_net_worth"; // 高净值客户
    public static final String TAG_INVESTOR = "investor"; // 投资客
    public static final String TAG_SINGLE_BRAND_HOLDING = "single_brand_holding"; // 单一品牌持有
    public static final String TAG_CROSS_CITY_PROPERTY = "cross_city_property"; // 跨城市置业经历
    public static final String TAG_GROWTH_CUSTOMER = "growth_customer"; // 成长型客户
    public static final String TAG_TOURISM_RETIREMENT = "tourism_retirement_property"; // 文旅养老置业经历
    public static final String TAG_FACILITY_PREFERENCE = "facility_preference"; // 配套设施偏好
    public static final String TAG_DESIGN_PREFERENCE = "design_preference"; // 设计规划偏好
    public static final String TAG_QUALITY_PREFERENCE = "quality_preference"; // 质量做工偏好
    public static final String TAG_DECORATION_PREFERENCE = "decoration_preference"; // 装修品质偏好
    public static final String TAG_PROPERTY_SERVICE = "property_service_preference"; // 物业服务品质偏好
    public static final String TAG_PROMISE_FULFILLMENT = "promise_fulfillment_preference"; // 承诺兑现偏好
    public static final String TAG_HIGH_STANDARD = "high_standard_preference"; // 高标准偏好

    // POI展示相关常量
    public static final int DEFAULT_MAX_POI_PER_TYPE = 5;
    public static final int NEARBY_POI_DISTANCE_THRESHOLD = 500; // 单位：米
    
    // 指标得分区间
    public static final int SCORE_VERY_LOW = 1;
    public static final int SCORE_LOW_MIN = 2;
    public static final int SCORE_LOW_MAX = 3;
    public static final int SCORE_HIGH_MIN = 4;
    public static final int SCORE_HIGH_MAX = 5;
    public static final int SCORE_NOT_RATED = -1;
    
    // 物业费区间阈值
    public static final double PROPERTY_FEE_FIRST_BUYER = 3.0; // 首置首改项目物业费阈值
    public static final double PROPERTY_FEE_IMPROVED = 5.0;    // 改善项目物业费阈值
    public static final double PROPERTY_FEE_HIGH_END = 8.0;    // 高端项目物业费阈值
    
    // 分析阈值
    public static final double PREFERRED_CATEGORY_THRESHOLD = 0.5; // 偏好类别占比阈值
} 
package com.dipspro.modules.profile.service;

import java.util.List;
import java.util.Map;

import com.dipspro.modules.profile.dto.ProjectsQueryResult;

/**
 * 项目数据查询服务接口
 * 
 * <AUTHOR>
 * @since 2025-06-03 14:51:45
 */
public interface ProjectDataQueryService {

    /**
     * 根据楼盘名称精确查询
     * 
     * @param name 楼盘名称
     * @return 查询结果
     */
    List<Map<String, Object>> queryProjectByExactName(String name);

    /**
     * 根据楼盘名称模糊查询
     * 
     * @param name 楼盘名称
     * @return 查询结果
     */
    List<Map<String, Object>> queryProjectByFuzzyName(String name);

    /**
     * 查询周边楼盘
     * 
     * @param lat      纬度
     * @param lng      经度
     * @param radiusKm 搜索半径（公里）
     * @return 查询结果
     */
    List<Map<String, Object>> queryNearbyProjects(Double lat, Double lng, double radiusKm);

    /**
     * 从手机号获取有效的项目列表和原始统计数据
     * 
     * @param mobile 手机号
     * @return 包含有效项目列表和原始统计数据的查询结果
     */
    ProjectsQueryResult getValidProjectsFromMobile(String mobile);
}
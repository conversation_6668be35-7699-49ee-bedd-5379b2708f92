package com.dipspro.modules.tenant.controller;

import com.dipspro.common.dto.ApiResponse;
import com.dipspro.modules.tenant.dto.*;
import com.dipspro.modules.tenant.service.TenantCacheService;
import com.dipspro.modules.tenant.service.TenantService;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * 租户管理控制器
 */
@RestController
@RequestMapping("/api/sys/tenant")
@Slf4j
public class TenantController {

    @Autowired
    private TenantService tenantService;

    @Autowired
    private TenantCacheService tenantCacheService;

    /**
     * 分页查询租户列表
     */
    @GetMapping("/list")
    public ApiResponse<Page<TenantResponseDto>> getTenantList(TenantQueryDto queryDto) {
        try {
            Page<TenantResponseDto> result = tenantService.getTenantList(queryDto);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("查询租户列表失败", e);
            return ApiResponse.error("查询租户列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取租户详情
     */
    @GetMapping("/detail")
    public ApiResponse<TenantResponseDto> getTenantDetail(@RequestParam Long id) {
        try {
            TenantResponseDto result = tenantService.getTenantById(id);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("获取租户详情失败，ID: {}", id, e);
            return ApiResponse.error("获取租户详情失败: " + e.getMessage());
        }
    }

    /**
     * 创建租户
     */
    @PostMapping("/create")
    public ApiResponse<TenantResponseDto> createTenant(@Valid @RequestBody TenantCreateDto createDto) {
        try {
            TenantResponseDto result = tenantService.createTenant(createDto);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("创建租户失败", e);
            return ApiResponse.error("创建租户失败: " + e.getMessage());
        }
    }

    /**
     * 更新租户
     */
    @PostMapping("/edit")
    public ApiResponse<TenantResponseDto> updateTenant(@RequestParam Long id,
            @Valid @RequestBody TenantUpdateDto updateDto) {
        try {
            TenantResponseDto result = tenantService.updateTenant(id, updateDto);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("更新租户失败，ID: {}", id, e);
            return ApiResponse.error("更新租户失败: " + e.getMessage());
        }
    }

    /**
     * 删除租户
     */
    @PostMapping("/delete")
    public ApiResponse<Void> deleteTenant(@RequestParam Long id) {
        try {
            tenantService.deleteTenant(id);
            return ApiResponse.success(null);
        } catch (Exception e) {
            log.error("删除租户失败，ID: {}", id, e);
            return ApiResponse.error("删除租户失败: " + e.getMessage());
        }
    }

    /**
     * 批量删除租户
     */
    @PostMapping("/batch-delete")
    public ApiResponse<Void> deleteTenants(@RequestBody List<Long> ids) {
        try {
            tenantService.deleteTenants(ids);
            return ApiResponse.success(null);
        } catch (Exception e) {
            log.error("批量删除租户失败，IDs: {}", ids, e);
            return ApiResponse.error("批量删除租户失败: " + e.getMessage());
        }
    }

    /**
     * 更新租户状态
     */
    @PostMapping("/update-status")
    public ApiResponse<Void> updateTenantStatus(@RequestParam Long id, @RequestParam Integer status) {
        try {
            tenantService.updateTenantStatus(id, status);
            return ApiResponse.success(null);
        } catch (Exception e) {
            log.error("更新租户状态失败，ID: {}, 状态: {}", id, status, e);
            return ApiResponse.error("更新租户状态失败: " + e.getMessage());
        }
    }

    /**
     * 检查租户编码是否存在
     */
    @GetMapping("/check-tenant-code")
    public ApiResponse<Boolean> checkTenantCodeExists(@RequestParam String tenantCode,
            @RequestParam(required = false) Long excludeId) {
        try {
            boolean exists = tenantService.checkTenantCodeExists(tenantCode, excludeId);
            return ApiResponse.success(exists);
        } catch (Exception e) {
            log.error("检查租户编码失败，编码: {}", tenantCode, e);
            return ApiResponse.error("检查租户编码失败: " + e.getMessage());
        }
    }

    /**
     * 检查租户名称是否存在
     */
    @GetMapping("/check-tenant-name")
    public ApiResponse<Boolean> checkTenantNameExists(@RequestParam String tenantName,
            @RequestParam(required = false) Long excludeId) {
        try {
            boolean exists = tenantService.checkTenantNameExists(tenantName, excludeId);
            return ApiResponse.success(exists);
        } catch (Exception e) {
            log.error("检查租户名称失败，名称: {}", tenantName, e);
            return ApiResponse.error("检查租户名称失败: " + e.getMessage());
        }
    }

    /**
     * 检查域名是否存在
     */
    @GetMapping("/check-domain")
    public ApiResponse<Boolean> checkDomainExists(@RequestParam String domain,
            @RequestParam(required = false) Long excludeId) {
        try {
            boolean exists = tenantService.checkDomainExists(domain, excludeId);
            return ApiResponse.success(exists);
        } catch (Exception e) {
            log.error("检查域名失败，域名: {}", domain, e);
            return ApiResponse.error("检查域名失败: " + e.getMessage());
        }
    }

    /**
     * 获取所有启用的租户
     */
    @GetMapping("/active")
    public ApiResponse<List<TenantResponseDto>> getAllActiveTenants() {
        try {
            List<TenantResponseDto> result = tenantService.getAllActiveTenants();
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("获取启用租户列表失败", e);
            return ApiResponse.error("获取启用租户列表失败: " + e.getMessage());
        }
    }

    /**
     * 为所有现有租户填充encrypt字段
     */
    @PostMapping("/fill-encrypt")
    public ApiResponse<Void> fillEncryptForExistingTenants() {
        try {
            tenantService.fillEncryptForExistingTenants();
            return ApiResponse.success(null, "成功为现有租户填充encrypt字段");
        } catch (Exception e) {
            log.error("填充encrypt字段失败", e);
            return ApiResponse.error("填充encrypt字段失败: " + e.getMessage());
        }
    }

    /**
     * 检查encrypt字段是否存在
     */
    @GetMapping("/check-encrypt")
    public ApiResponse<Boolean> checkEncryptExists(@RequestParam String encrypt,
            @RequestParam(required = false) Long excludeId) {
        try {
            boolean exists = tenantService.checkEncryptExists(encrypt, excludeId);
            return ApiResponse.success(exists);
        } catch (Exception e) {
            log.error("检查encrypt字段是否存在失败", e);
            return ApiResponse.error("检查encrypt字段是否存在失败: " + e.getMessage());
        }
    }

    /**
     * 获取租户二维码访问链接
     */
    @GetMapping("/qrcode-url")
    public ApiResponse<String> getTenantQrCodeUrl(@RequestParam Long tenantId) {
        try {
            String qrCodeUrl = tenantService.getTenantQrCodeUrl(tenantId);
            if (qrCodeUrl != null) {
                return ApiResponse.success(qrCodeUrl);
            } else {
                return ApiResponse.error("租户二维码不存在，请先生成二维码");
            }
        } catch (Exception e) {
            log.error("获取租户二维码链接失败，租户ID: {}", tenantId, e);
            return ApiResponse.error("获取二维码链接失败: " + e.getMessage());
        }
    }

    /**
     * CSV导入租户数据
     */
    @PostMapping("/import-csv")
    public ApiResponse<TenantImportResultDto> importTenantFromCsv(@RequestParam("file") MultipartFile file) {
        try {
            if (file.isEmpty()) {
                return ApiResponse.error("文件不能为空");
            }

            if (!file.getOriginalFilename().toLowerCase().endsWith(".csv")) {
                return ApiResponse.error("只支持CSV格式文件");
            }

            String csvContent = new String(file.getBytes(), StandardCharsets.UTF_8);
            TenantImportResultDto result = tenantService.importTenantFromCsv(csvContent);

            if (result.getAllSuccess()) {
                return ApiResponse.success(result, "CSV导入成功");
            } else {
                return ApiResponse.error(result, "CSV导入部分失败，请查看详细信息");
            }

        } catch (Exception e) {
            log.error("CSV导入租户数据失败", e);
            return ApiResponse.error("CSV导入失败: " + e.getMessage());
        }
    }

    /**
     * 导入租户配置CSV文件（医美机构专用）
     */
    @PostMapping("/import-config-csv")
    public ApiResponse<TenantImportResultDto> importTenantConfigFromCsv(@RequestParam("file") MultipartFile file) {
        try {
            if (file.isEmpty()) {
                return ApiResponse.error("文件不能为空");
            }

            // 检查文件类型
            String fileName = file.getOriginalFilename();
            if (fileName == null || !fileName.toLowerCase().endsWith(".csv")) {
                return ApiResponse.error("只支持CSV文件格式");
            }

            // 读取文件内容
            String csvContent = new String(file.getBytes(), StandardCharsets.UTF_8);
            if (csvContent.trim().isEmpty()) {
                return ApiResponse.error("文件内容不能为空");
            }

            // 调用服务层导入租户配置数据
            TenantImportResultDto result = tenantService.importTenantConfigFromCsv(csvContent);

            log.info("租户配置CSV导入完成，成功: {}, 失败: {}", result.getSuccessCount(), result.getFailureCount());
            return ApiResponse.success(result);

        } catch (Exception e) {
            log.error("导入租户配置CSV文件失败", e);
            return ApiResponse.error("导入失败: " + e.getMessage());
        }
    }

    // /**
    // * 根据套餐类型获取租户列表
    // */
    // @GetMapping("/by-package-type")
    // public ApiResponse<List<TenantResponseDto>>
    // getTenantsByPackageType(@RequestParam String packageType) {
    // try {
    // List<TenantResponseDto> result =
    // tenantService.getTenantsByPackageType(packageType);
    // return ApiResponse.success(result);
    // } catch (Exception e) {
    // log.error("根据套餐类型获取租户列表失败，套餐类型: {}", packageType, e);
    // return ApiResponse.error("根据套餐类型获取租户列表失败: " + e.getMessage());
    // }
    // }

    /**
     * 获取租户配置列表
     */
    @GetMapping("/configs")
    public ApiResponse<List<TenantConfigResponseDto>> getTenantConfigs(@RequestParam Long tenantId) {
        try {
            List<TenantConfigResponseDto> result = tenantService.getTenantConfigs(tenantId);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("获取租户配置失败，租户ID: {}", tenantId, e);
            return ApiResponse.error("获取租户配置失败: " + e.getMessage());
        }
    }

    /**
     * 获取单个租户及其配置信息
     */
    @GetMapping("/with-configs")
    public ApiResponse<TenantWithConfigDto> getTenantWithConfigs(@RequestParam Long tenantId) {
        try {
            TenantWithConfigDto result = tenantService.getTenantWithConfigs(tenantId);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("获取租户及配置信息失败，租户ID: {}", tenantId, e);
            return ApiResponse.error("获取租户及配置信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取所有租户及其配置信息
     */
    @GetMapping("/all-with-configs")
    public ApiResponse<List<TenantWithConfigDto>> getAllTenantsWithConfigs() {
        try {
            List<TenantWithConfigDto> result = tenantService.getAllTenantsWithConfigs();
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("获取所有租户及配置信息失败", e);
            return ApiResponse.error("获取所有租户及配置信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取租户海报列表
     */
    @GetMapping("/posters")
    public ApiResponse<List<TenantPosterDto>> getTenantPosters(@RequestParam Long tenantId) {
        try {
            List<TenantPosterDto> result = tenantService.getTenantPosters(tenantId);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("获取租户海报列表失败，租户ID: {}", tenantId, e);
            return ApiResponse.error("获取租户海报列表失败: " + e.getMessage());
        }
    }

    /**
     * 刷新租户缓存
     */
    @PostMapping("/refresh-cache")
    public ApiResponse<String> refreshTenantCache() {
        try {
            tenantCacheService.refreshCache();
            int cacheSize = tenantCacheService.getCacheSize();
            String message = String.format("租户缓存刷新成功，当前缓存数量: %d", cacheSize);
            log.info(message);
            return ApiResponse.success(message);
        } catch (Exception e) {
            log.error("刷新租户缓存失败", e);
            return ApiResponse.error("刷新租户缓存失败: " + e.getMessage());
        }
    }

    /**
     * 获取租户缓存状态
     */
    @GetMapping("/cache-status")
    public ApiResponse<Object> getTenantCacheStatus() {
        try {
            boolean initialized = tenantCacheService.isCacheInitialized();
            int cacheSize = tenantCacheService.getCacheSize();

            java.util.Map<String, Object> status = new java.util.HashMap<>();
            status.put("initialized", initialized);
            status.put("cacheSize", cacheSize);
            status.put("message", initialized ? "缓存已初始化" : "缓存未初始化");

            return ApiResponse.success(status);
        } catch (Exception e) {
            log.error("获取租户缓存状态失败", e);
            return ApiResponse.error("获取租户缓存状态失败: " + e.getMessage());
        }
    }

    /**
     * 初始化医美机构问卷配置
     */
    @PostMapping("/init-ym-exam-config")
    public ApiResponse<String> initYmExamConfig() {
        try {
            log.info("收到初始化医美机构问卷配置请求");
            int successCount = tenantService.initializeYmExamConfigForAllOrganizationTenants();
            String message = String.format("医美机构问卷配置初始化完成，成功配置%d个租户", successCount);
            return ApiResponse.success(message);
        } catch (Exception e) {
            log.error("初始化医美机构问卷配置失败", e);
            return ApiResponse.error("初始化失败: " + e.getMessage());
        }
    }

    /**
     * 手动生成租户问卷二维码
     */
    @PostMapping("/generate-qrcode")
    public ApiResponse<String> generateTenantQrCode(@RequestParam Long tenantId) {
        try {
            log.info("收到生成租户问卷二维码请求，租户ID: {}", tenantId);
            String result = tenantService.generateTenantQrCode(tenantId);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("生成租户问卷二维码失败，租户ID: {}", tenantId, e);
            return ApiResponse.error("生成二维码失败: " + e.getMessage());
        }
    }

    // === 海报模板管理相关接口 ===

    /**
     * 为所有租户批量初始化海报模板配置
     */
    @PostMapping("/init-poster-templates")
    public ApiResponse<String> initPosterTemplatesForAllTenants() {
        try {
            log.info("收到批量初始化海报模板配置请求");
            int successCount = tenantService.initializePosterTemplatesForAllTenants();
            String message = String.format("海报模板配置批量初始化完成，成功初始化%d个租户", successCount);
            return ApiResponse.success(message);
        } catch (Exception e) {
            log.error("批量初始化海报模板配置失败", e);
            return ApiResponse.error("批量初始化失败: " + e.getMessage());
        }
    }

    /**
     * 为单个租户初始化海报模板配置
     */
    @PostMapping("/init-poster-template")
    public ApiResponse<String> initPosterTemplateForTenant(@RequestParam Long tenantId) {
        try {
            log.info("收到为租户初始化海报模板配置请求，租户ID: {}", tenantId);
            boolean success = tenantService.initializePosterTemplatesForTenant(tenantId);
            if (success) {
                return ApiResponse.success("租户海报模板配置初始化成功");
            } else {
                return ApiResponse.success("租户已存在海报模板配置，跳过初始化");
            }
        } catch (Exception e) {
            log.error("为租户初始化海报模板配置失败，租户ID: {}", tenantId, e);
            return ApiResponse.error("初始化失败: " + e.getMessage());
        }
    }

    /**
     * 检查并生成租户海报（测试接口）
     */
    @PostMapping("/generate-posters")
    public ApiResponse<String> generatePostersForTenant(@RequestParam Long tenantId) {
        try {
            log.info("收到检查并生成租户海报请求，租户ID: {}", tenantId);
            String result = tenantService.checkAndGeneratePostersForTenant(tenantId);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("检查并生成租户海报失败，租户ID: {}", tenantId, e);
            return ApiResponse.error("生成海报失败: " + e.getMessage());
        }
    }

    /**
     * 导出所有租户信息到Excel文件（保存到磁盘）
     */
    @GetMapping("/export-excel")
    public ApiResponse<String> exportTenantsToExcel() {
        long startTime = System.currentTimeMillis();
        log.info("开始导出租户Excel数据到磁盘...");
        
        try {
            // 获取导出数据
            log.info("正在查询租户数据...");
            List<TenantExcelExportDto> exportData = tenantService.getAllTenantsForExcelExport();
            log.info("查询到{}条租户记录，开始生成Excel文件...", exportData.size());
            
            // 创建Excel工作簿
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("租户信息");
            log.debug("Excel工作簿创建完成");
            
            // 创建标题行
            Row headerRow = sheet.createRow(0);
            String[] headers = {
                "租户ID", "行业类型", "租户类型", "租户名称", "唯一码", 
                "联系电话", "业务地址", "业务城市", "业务区域", "usci"
            };
            
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
            }
            log.debug("Excel标题行创建完成，共{}列", headers.length);
            
            // 填充数据行
            for (int i = 0; i < exportData.size(); i++) {
                Row dataRow = sheet.createRow(i + 1);
                TenantExcelExportDto data = exportData.get(i);
                
                dataRow.createCell(0).setCellValue(data.getTenantId() != null ? data.getTenantId().toString() : "");
                dataRow.createCell(1).setCellValue(data.getIndustryType() != null ? data.getIndustryType() : "");
                dataRow.createCell(2).setCellValue(data.getTenantType() != null ? data.getTenantType() : "");
                dataRow.createCell(3).setCellValue(data.getTenantName() != null ? data.getTenantName() : "");
                dataRow.createCell(4).setCellValue(data.getEncrypt() != null ? data.getEncrypt() : "");
                dataRow.createCell(5).setCellValue(data.getContactPhone() != null ? data.getContactPhone() : "");
                dataRow.createCell(6).setCellValue(data.getBusinessAddress() != null ? data.getBusinessAddress() : "");
                dataRow.createCell(7).setCellValue(data.getBusinessCity() != null ? data.getBusinessCity() : "");
                dataRow.createCell(8).setCellValue(data.getBusinessDistrict() != null ? data.getBusinessDistrict() : "");
                dataRow.createCell(9).setCellValue(data.getBusinessUsci() != null ? data.getBusinessUsci() : "");
                
                // 每处理100行记录输出一次进度日志
                if ((i + 1) % 100 == 0) {
                    log.debug("已处理{}行数据", i + 1);
                }
            }
            log.info("Excel数据填充完成，共{}行数据", exportData.size());
            
            // 自动调整列宽
            log.debug("开始调整Excel列宽...");
            for (int i = 0; i < headers.length; i++) {
                sheet.autoSizeColumn(i);
            }
            log.debug("Excel列宽调整完成");
            
            // 创建导出目录
            String exportDir = "exports";
            java.nio.file.Path exportPath = java.nio.file.Paths.get(exportDir);
            if (!java.nio.file.Files.exists(exportPath)) {
                java.nio.file.Files.createDirectories(exportPath);
                log.debug("创建导出目录: {}", exportPath.toAbsolutePath());
            }
            
            // 生成文件名（包含时间戳）
            String timestamp = java.time.LocalDateTime.now().format(
                java.time.format.DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")
            );
            String fileName = String.format("tenant_export_%s.xlsx", timestamp);
            java.nio.file.Path filePath = exportPath.resolve(fileName);
            
            // 将工作簿写入磁盘文件
            log.debug("开始写入Excel文件到磁盘: {}", filePath.toAbsolutePath());
            try (java.io.FileOutputStream fileOut = new java.io.FileOutputStream(filePath.toFile())) {
                workbook.write(fileOut);
            }
            workbook.close();
            
            // 获取文件大小
            long fileSize = java.nio.file.Files.size(filePath);
            long processingTime = System.currentTimeMillis() - startTime;
            
            String absolutePath = filePath.toAbsolutePath().toString();
            log.info("租户Excel导出成功！共导出{}条记录，文件大小{}KB，耗时{}ms，文件路径: {}", 
                    exportData.size(), fileSize / 1024, processingTime, absolutePath);
            
            return ApiResponse.success(absolutePath);
                    
        } catch (IOException e) {
            long processingTime = System.currentTimeMillis() - startTime;
            log.error("导出租户Excel文件失败，耗时{}ms", processingTime, e);
            return ApiResponse.error("导出Excel文件失败: " + e.getMessage());
        } catch (Exception e) {
            long processingTime = System.currentTimeMillis() - startTime;
            log.error("导出租户Excel数据失败，耗时{}ms", processingTime, e);
            return ApiResponse.error("导出数据失败: " + e.getMessage());
        }
    }
}
package com.dipspro.modules.system.controller;

import com.alibaba.fastjson.JSON;
import com.dipspro.modules.system.dto.NotificationDto;
import com.dipspro.modules.system.service.NotificationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025-07-31
 **/
@RestController
@RequestMapping("/api/sys/notification")
@Slf4j
public class NotificationController {

    @Autowired
    private NotificationService notificationService;

    @GetMapping("/findByUserIdAndStatus")
    public List<NotificationDto> findByUserIdAndStatus(@RequestParam Long userId, @RequestParam String status) {
        return notificationService.findByUserIdAndStatus(userId, status);
    }

    @PostMapping("/readNotification")
    public void readNotification(@RequestParam Long id) {
        notificationService.readNotification(id);
    }

    @PutMapping("/createNotification")
    public void createNotification(@RequestParam String params) {
        Map<String, Object> map = JSON.parseObject(params);
        notificationService.createNotification(map);
    }
}
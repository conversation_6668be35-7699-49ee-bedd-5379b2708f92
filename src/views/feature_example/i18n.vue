<route lang="yaml">
meta:
  enabled: false
</route>

<i18n lang="yaml">
zh-cn:
  intro: 除了支持全局多语言切换，还支持 Vue 单文件模式语言切换，你可以尝试在这个页面点击右上角的语言切换试试
  form:
    name: 姓名
    age: 年龄
  formRules:
    name: 请输入姓名
    age: 请输入年龄
zh-tw:
  intro: 除了支持全局多語言切換，還支持 Vue 單文件模式語言切換，你可以嘗試在這個頁面點擊右上角的語言切換試試
  form:
    name: 姓名
    age: 年齡
  formRules:
    name: 請輸入姓名
    age: 請輸入年齡
en:
  intro: In addition to global multi-language switch, also support Vue single file mode language switch, you can try to click on the top right corner of the page to switch language
  form:
    name: Name
    age: Age
  formRules:
    name: Please enter name
    age: Please enter age
</i18n>

<script setup lang="ts">
import { getLocales, i18n } from '@/locales'
import useSettingsStore from '@/store/modules/settings'
import dayjs from '@/utils/dayjs'
import { useI18n } from 'vue-i18n'
import { toast } from 'vue-sonner'

const { t } = useI18n()
const settingsStore = useSettingsStore()

const page = ref(1)
const pageSize = ref(100)

function handleSizeChange(val: any) {
  toast(`每页 ${val} 条`)
}

function handleCurrentChange(val: any) {
  toast(`当前页: ${val}`)
}

const form = ref({
  name: '',
  age: '',
})

const formRules = ref({
  name: [{ required: true, message: () => t('formRules.name'), trigger: 'blur' }],
  age: [{ required: true, message: () => t('formRules.age'), trigger: 'blur' }],
})

const monthWeek = ref('')
watch(() => settingsStore.lang, () => {
  monthWeek.value = dayjs().format('MMMM-dddd')
}, {
  immediate: true,
})

function lazyload(lang: string) {
  const locales = getLocales()
  if (!locales) {
    return
  }
  // 模拟异步载入，新增的语言包数据需要和框架默认数据合并
  if (lang === 'zh-cn') {
    Object.assign(locales['zh-cn'], {
      hello: {
        world: '你好，世界！',
      },
    })
    i18n.global.setLocaleMessage(lang, locales['zh-cn'])
  }
  else {
    Object.assign(locales.en, {
      hello: {
        world: 'Hello World !',
      },
    })
    i18n.global.setLocaleMessage(lang, locales.en)
  }
  toast.success('载入成功，你可以切换语言查看效果')
}
</script>

<template>
  <div>
    <FaPageHeader :title="t('route.feature.i18n')" :description="t('intro')" />
    <FaPageMain title="Element 组件">
      <ElPagination v-model:current-page="page" v-model:page-size="pageSize" :page-sizes="[100, 200, 300, 400]" layout="total, sizes, prev, pager, next, jumper" :total="400" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </FaPageMain>
    <FaPageMain title="表单验证">
      <ElForm v-model="form" :rules="formRules" label-width="100px">
        <ElFormItem :label="t('form.name')" prop="name">
          <ElInput v-model="form.name" />
        </ElFormItem>
        <ElFormItem :label="t('form.age')" prop="age">
          <ElInput v-model="form.age" />
        </ElFormItem>
      </ElForm>
    </FaPageMain>
    <FaPageMain title="Dayjs">
      {{ monthWeek }}
    </FaPageMain>
    <FaPageMain title="延迟加载">
      <ElButton @click="lazyload('zh-cn')">
        载入中文
      </ElButton>
      <ElButton @click="lazyload('en')">
        载入英文
      </ElButton>
      <p>
        <!-- eslint-disable-next-line @intlify/vue-i18n/no-missing-keys -->
        {{ t('hello.world') }}
      </p>
    </FaPageMain>
  </div>
</template>

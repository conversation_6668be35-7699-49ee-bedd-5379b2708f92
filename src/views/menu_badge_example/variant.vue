<route lang="yaml">
meta:
  enabled: false
</route>

<script setup lang="ts">
import useMenuBadgeStore from '@/store/modules/menuBadge'

const menuBadgeStore = useMenuBadgeStore()

function change(val: 'default' | 'secondary' | 'destructive') {
  menuBadgeStore.setVariant(val)
}
</script>

<template>
  <div>
    <FaPageHeader title="标记变体" description="搭配 Pinia 可实现动态设置" />
    <FaPageMain>
      <div class="space-x-2">
        <FaButton :disabled="menuBadgeStore.variant === 'default'" @click="change('default')">
          default
        </FaButton>
        <FaButton :disabled="menuBadgeStore.variant === 'secondary'" @click="change('secondary')">
          secondary
        </FaButton>
        <FaButton :disabled="menuBadgeStore.variant === 'destructive'" @click="change('destructive')">
          destructive
        </FaButton>
      </div>
    </FaPageMain>
  </div>
</template>

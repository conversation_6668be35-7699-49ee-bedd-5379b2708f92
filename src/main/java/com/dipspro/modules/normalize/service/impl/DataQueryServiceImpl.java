package com.dipspro.modules.normalize.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.dipspro.modules.chat.dto.MessageContent;
import com.dipspro.modules.normalize.dto.BatchUserProfileRequest;
import com.dipspro.modules.normalize.dto.ProjectProfileRequest;
import com.dipspro.modules.normalize.service.DataQueryService;
import com.dipspro.modules.profile.dto.ProjectProfileDto;
import com.dipspro.modules.profile.dto.UserProfileDto;
import com.dipspro.modules.profile.service.ProjectProfileService;
import com.dipspro.modules.profile.service.UserProfileService;
import com.dipspro.util.MaskUtil;
import com.dipspro.util.StringParseUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * 数据查询服务实现类 - 重构后的委托模式
 * 
 * <AUTHOR>
 * @since 2025-06-03 14:51:45
 * @apiNote 数据查询接口，委托给专门的服务处理
 */
@Slf4j
@Service
public class DataQueryServiceImpl implements DataQueryService {

    @Autowired
    private UserProfileService userProfileService;

    @Autowired
    private ProjectProfileService projectProfileService;

    @Override
    public UserProfileDto userProfile(String mobile) {
        log.info("委托给UserProfileService处理用户画像查询，手机号: {}", MaskUtil.maskMobile(mobile));
        return userProfileService.queryUserProfile(mobile);
    }

    @Override
    public List<MessageContent> batchUserProfile(BatchUserProfileRequest request) {
        // 从客户特征字符串解析出手机号列表
        List<String> mobiles = StringParseUtil.parseAndValidateCustomerFeatures(request.getCustomerFeatures());

        log.info("委托给UserProfileService处理批量用户画像查询，从客户特征解析出手机号数量: {}", mobiles.size());
        long startTime = System.currentTimeMillis();

        // 调用UserProfileService的批量查询方法
        Map<String, UserProfileDto> userProfileMap = userProfileService.batchQueryUserProfile(
                mobiles,
                request.getShowDetail() != null ? request.getShowDetail() : false);

        // 合并所有成功查询的结果到一个List中
        List<MessageContent> allResults = new ArrayList<>();
        int successCount = 0;
        int failedCount = 0;

        for (Map.Entry<String, UserProfileDto> entry : userProfileMap.entrySet()) {
            String mobile = entry.getKey();
            UserProfileDto userProfile = entry.getValue();

            if (userProfile != null && userProfile.getProfiles() != null && !userProfile.getProfiles().isEmpty()) {
                // 查询成功，添加手机号标识和用户画像内容
                allResults.add(MessageContent.text("手机号: " + MaskUtil.maskMobile(mobile)));
                allResults.addAll(userProfile.getProfiles());
                allResults.add(MessageContent.text("---")); // 分隔符
                successCount++;
            } else {
                // 查询失败，添加失败信息
                allResults.add(MessageContent.text("手机号: " + MaskUtil.maskMobile(mobile) + " - 未找到用户画像数据"));
                failedCount++;
            }
        }

        // 添加统计信息到结果开头
        long endTime = System.currentTimeMillis();
        List<MessageContent> finalResults = new ArrayList<>();
        finalResults.add(MessageContent.text(String.format("批量查询统计: 总数 %d, 成功 %d, 失败 %d, 耗时 %dms",
                mobiles.size(), successCount, failedCount, (endTime - startTime))));
        finalResults.add(MessageContent.text("=".repeat(50)));
        finalResults.addAll(allResults);

        log.info("批量用户画像查询完成，总数: {}, 成功: {}, 失败: {}, 耗时: {}ms",
                mobiles.size(), successCount, failedCount, (endTime - startTime));

        return finalResults;
    }

    @Override
    public ProjectProfileDto projectProfile(ProjectProfileRequest request) {
        log.info("委托给ProjectProfileService处理楼盘画像查询，楼盘名称: {}", request.getName());
        return projectProfileService.queryProjectProfile(request);
    }

    @Override
    public ProjectProfileDto projectProfileSalesInfo(ProjectProfileRequest request) {
        log.info("委托给ProjectProfileService处理楼盘画像查询，楼盘名称: {}", request.getName());
        return projectProfileService.projectProfileSalesInfo(request);
    }

    @Override
    public ProjectProfileDto projectProfileSurrounding(ProjectProfileRequest request) {
        log.info("委托给ProjectProfileService处理楼盘画像查询，楼盘名称: {}", request.getName());
        return projectProfileService.projectProfileSurrounding(request);
    }

    @Override
    public ProjectProfileDto projectProfileAna(ProjectProfileRequest request) {
        log.info("委托给ProjectProfileService处理楼盘画像查询，楼盘名称: {}", request.getName());
        return projectProfileService.projectProfileAna(request);
    }
}

package com.dipspro.security;

import java.io.IOException;

import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.stereotype.Component;

import com.dipspro.common.dto.ApiResponse;
import com.dipspro.util.JwtUtil;
import com.fasterxml.jackson.databind.ObjectMapper;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * JWT认证入口点
 * 当用户访问受保护的资源但未提供有效的JWT令牌时，会调用此类
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class JwtAuthenticationEntryPoint implements AuthenticationEntryPoint {

    private final JwtUtil jwtUtil;

    @Override
    public void commence(HttpServletRequest request, HttpServletResponse response,
                         AuthenticationException authException) throws IOException {
        
        String requestURI = request.getRequestURI();
        String method = request.getMethod();
        String authHeader = request.getHeader("Authorization");
        String threadName = Thread.currentThread().getName();
        
        // 记录详细的认证失败信息
        log.error("未授权访问: {} {} - {} 线程: {}", method, requestURI, authException.getMessage(), threadName);
        
        // 记录请求的详细信息
        log.error("认证失败详情 - 远程地址: {}, User-Agent: {}, Referer: {}", 
                request.getRemoteAddr(), 
                request.getHeader("User-Agent"), 
                request.getHeader("Referer"));
        
        // 检查请求属性，看是否经过了JWT过滤器
        Object jwtFilterProcessed = request.getAttribute("JWT_FILTER_PROCESSED");
        if (jwtFilterProcessed != null) {
            log.error("请求已经过JWT过滤器处理，但仍然被拒绝访问");
        } else {
            log.error("请求可能没有经过JWT过滤器处理！");
        }
        
        // 检查Spring Security上下文
        if (org.springframework.security.core.context.SecurityContextHolder.getContext().getAuthentication() != null) {
            log.error("Spring Security上下文存在认证信息: {}", 
                    org.springframework.security.core.context.SecurityContextHolder.getContext().getAuthentication().getName());
        } else {
            log.error("Spring Security上下文无认证信息");
        }
        
        // 检查所有请求属性
        log.error("请求属性列表:");
        java.util.Enumeration<String> attributeNames = request.getAttributeNames();
        while (attributeNames.hasMoreElements()) {
            String attrName = attributeNames.nextElement();
            Object attrValue = request.getAttribute(attrName);
            log.error("  - {}: {}", attrName, attrValue);
        }
        
        // 分析认证失败的具体原因
        String errorMessage;
        if (authHeader == null) {
            errorMessage = "缺少认证信息。请在请求头中添加 'Authorization: Bearer <token>'";
            log.warn("请求缺少Authorization头: {} {}", method, requestURI);
        } else if (!authHeader.startsWith("Bearer ")) {
            errorMessage = "认证格式错误。请使用 'Authorization: Bearer <token>' 格式";
            log.warn("Authorization头格式错误: {} {}, 头信息: {}", method, requestURI, authHeader);
        } else {
            String token = authHeader.replace("Bearer ", "");
            boolean isValid = jwtUtil.validateAccessToken(token);
            
            if (isValid) {
                errorMessage = "认证令牌有效但访问被拒绝。请检查权限配置";
                log.warn("JWT令牌有效但访问被拒绝: {} {}", method, requestURI);
            } else {
                errorMessage = "认证令牌无效或已过期。请重新登录获取新的令牌";
                log.warn("JWT令牌验证失败: {} {}", method, requestURI);
            }
        }

        response.setContentType("application/json;charset=UTF-8");
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);

        // 构建详细的错误响应
        ApiResponse<Object> result = ApiResponse.unauthorized(errorMessage);

        ObjectMapper mapper = new ObjectMapper();
        mapper.writeValue(response.getOutputStream(), result);
    }
} 
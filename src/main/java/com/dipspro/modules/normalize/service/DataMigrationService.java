package com.dipspro.modules.normalize.service;

import java.util.List;

import com.dipspro.modules.profile.dto.DataStatisticsResultDto;
import com.dipspro.modules.normalize.dto.ImportProgressInfo;
import com.dipspro.modules.profile.dto.MobileCombinedStatsDTO;
import com.dipspro.modules.profile.dto.MobileDataRecord;
import com.dipspro.modules.profile.dto.MobileProjectCountResultDTO;
import com.dipspro.modules.profile.dto.MobileStatsResultDto;
import com.dipspro.modules.normalize.dto.NormalizeData;

/**
 * 数据迁移服务接口
 */
public interface DataMigrationService {

    /**
     * 将MySQL数据导入到Redis中
     * 
     * @param batchSize 每批处理的记录数
     */
    void importDataToRedis(int batchSize);
    
    /**
     * 根据手机号查询对应的数据记录
     * 
     * @param mobile 手机号
     * @return 数据记录列表
     */
    List<MobileDataRecord> queryByMobile(String mobile);
    
    /**
     * 获取当前数据导入进度
     * 
     * @return 进度信息
     */
    ImportProgressInfo getImportProgress();
    
    /**
     * 根据手机号查询实际的名单数据内容
     * 
     * @param mobile 手机号
     * @return 名单数据列表，包含实际数据内容
     */
    List<NormalizeData> queryNameList(String mobile);
    
    /**
     * 根据手机号查询调研数据内容（表名以dataset_开头的数据）
     * 
     * @param mobile 手机号
     * @return 调研数据列表，包含实际数据内容
     */
    List<NormalizeData> queryDataset(String mobile);
    
    /**
     * 根据手机号查询名单数据并按项目分组统计结果
     * 
     * @param mobile 手机号
     * @return 统计结果对象，包含原始数据和按项目分组的统计
     */
    DataStatisticsResultDto queryNameListWithStatistics(String mobile);
    
    /**
     * 根据手机号查询调研数据并按项目分组统计结果
     * 
     * @param mobile 手机号
     * @return 统计结果对象，包含原始数据和按项目分组的统计
     */
    DataStatisticsResultDto queryDatasetWithStatistics(String mobile);
    
    /**
     * 清空所有手机号数据
     * 
     * @return 清除的键数量
     */
    int clearAllMobileData();
    
    /**
     * 关闭导入服务线程池
     * 用于手动关闭导入任务线程池，释放资源
     * 
     * @return 关闭是否成功
     */
    boolean shutdownImportService();

    /**
     * 根据手机号同时获取名单和数据集的统计信息
     * 
     * @param mobile 手机号
     * @return 合并的统计信息，包含名单和数据集的统计以及汇总数据
     */
    MobileCombinedStatsDTO getCombinedStatsByMobile(String mobile);
    
    /**
     * 查询满足条件的手机号列表，按记录数排序
     * 
     * @param queryType 查询类型：ALL、NAMELIST或DATASET
     * @param threshold 阈值，查询记录数大于该值的手机号
     * @param limit 限制返回条数
     * @return 手机号统计结果，包含手机号列表和记录数
     */
    MobileStatsResultDto queryTopMobilesByRecordCount(String queryType, int threshold, int limit);
    
    /**
     * 查询名单中同一个手机号项目数量大于N的记录
     * 
     * @param threshold 项目数量阈值，查询项目数量大于该值的手机号
     * @return 查询结果，包含手机号、项目数量和项目集合
     */
    MobileProjectCountResultDTO queryMobilesByProjectCount(int threshold);
} 
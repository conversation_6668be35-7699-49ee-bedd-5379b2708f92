# 中文句子转向量服务配置
embedding:
  model: 
    # ONNX模型文件路径（相对于项目根目录或绝对路径）
    path: ${embedding-path:}
    # 词汇表文件路径
    vocab-path: ${embedding-vocab-path:}
  
  thread-pool:
    # 线程池大小，建议设置为CPU核心数
    size: 8

# 日志配置
logging:
  level:
    com.dipspro.modules.embedding: DEBUG
    ai.onnxruntime: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# 服务器配置
server:
  # 增加请求超时时间，因为向量计算可能需要较长时间
  servlet:
    context-path: /
  tomcat:
    connection-timeout: 60000
    max-connections: 200
    threads:
      max: 200
      min-spare: 10

# Spring配置
spring:
  # 增加最大请求大小，支持批量文本处理
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
  
  # Jackson配置
  jackson:
    serialization:
      write-dates-as-timestamps: false
    default-property-inclusion: non_null

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when_authorized
  metrics:
    export:
      prometheus:
        enabled: true
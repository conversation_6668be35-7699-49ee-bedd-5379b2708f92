package com.dipspro.modules.ym.controller;

import com.dipspro.common.dto.ApiResponse;
import com.dipspro.modules.ym.dto.OrderDto;
import com.dipspro.modules.ym.dto.ProductDto;
import com.dipspro.modules.ym.dto.WxOrgDto;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 医美数据控制器
 */
@RestController
@RequestMapping("/api/ym")
@Slf4j
public class YmController {

    private final static String wx_open_id = "oq_Rp1707KdcNX-khPNmIRbIPcno";
    private final static String unionId = "omStD6IMD3DvOkkl1gcG90p3J-98";

    @PostMapping("/org_list")
    public ApiResponse<List<WxOrgDto>> getOrgList(@RequestParam RequestParam param) {
        log.info("获取机构列表，参数: {}", param);
        List<WxOrgDto> list = Lists.newArrayList();
        list.add(WxOrgDto.builder().id(1L).wxOpenId(wx_open_id).unionId(unionId).orgId("org_id_1").orgName("艺星广州天河一店").build());
        list.add(WxOrgDto.builder().id(2L).wxOpenId(wx_open_id).unionId(unionId).orgId("org_id_2").orgName("艺星广州黄埔一店").build());
        list.add(WxOrgDto.builder().id(3L).wxOpenId(wx_open_id).unionId(unionId).orgId("org_id_3").orgName("艺星广州珠江新城店").build());
        return ApiResponse.success(list);
    }

    @PostMapping("/order_list")
    public ApiResponse<List<OrderDto>> getOrderList(@RequestParam RequestParam param) {
        log.info("获取订单列表，参数：{}", param);
        List<OrderDto> list = Lists.newArrayList();

        ProductDto product1 = ProductDto.builder().id(1L).uuid("uuid_1").name("默认套餐").price(2999).build();
        ProductDto product2 = ProductDto.builder().id(1L).uuid("uuid_2").name("默认套餐").price(3999).build();
        ProductDto product3 = ProductDto.builder().id(1L).uuid("uuid_3").name("默认套餐").price(4999).build();

        list.add(OrderDto.builder().id(1L).wxOpenId(wx_open_id).unionId(unionId).orgId("org_id_1").product(product1)
                .startDate("2024-01-01").endDate("2024-08-31").build());
        list.add(OrderDto.builder().id(2L).wxOpenId(wx_open_id).unionId(unionId).orgId("org_id_2").product(product2)
                .startDate("2024-09-01").endDate("2025-02-28").build());
        list.add(OrderDto.builder().id(3L).wxOpenId(wx_open_id).unionId(unionId).orgId("org_id_3").product(product3)
                .startDate("2025-03-01").endDate("2025-07-31").build());
        return ApiResponse.success(list);
    }

}

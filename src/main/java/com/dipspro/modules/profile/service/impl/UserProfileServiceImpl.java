package com.dipspro.modules.profile.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import com.dipspro.modules.profile.dto.MobileCombinedStatsDTO;
import com.dipspro.modules.profile.dto.ProjectDetail;
import com.dipspro.modules.profile.dto.ProjectsQueryResult;
import com.dipspro.modules.profile.dto.UserProfileDto;
import com.dipspro.modules.profile.service.ProjectDataQueryService;
import com.dipspro.modules.profile.service.ProjectEnrichmentService;
import com.dipspro.modules.profile.service.UserProfileService;
import com.dipspro.util.MaskUtil;

import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;

/**
 * 用户画像服务实现类
 * 
 * <AUTHOR>
 * @since 2025-06-03 14:51:45
 */
@Slf4j
@Service
public class UserProfileServiceImpl implements UserProfileService {

    @Autowired
    private ProjectDataQueryService projectDataQueryService;

    @Autowired
    private ProjectEnrichmentService projectEnrichmentService;

    @Autowired
    private StringRedisTemplate redisTemplate;

    // 批量查询线程池
    private final ExecutorService batchQueryExecutor = Executors.newFixedThreadPool(10);

    @Override
    public UserProfileDto queryUserProfile(String mobile) {
        log.info("开始查询用户画像，手机号: {}", MaskUtil.maskMobile(mobile));

        // 从手机号获取有效的项目列表和原始统计数据
        ProjectsQueryResult queryResult = projectDataQueryService.getValidProjectsFromMobile(mobile);
        List<ProjectDetail> validProjects = queryResult.getProjects();
        MobileCombinedStatsDTO combinedStats = queryResult.getCombinedStats();

        // 为项目丰富所有数据
        projectEnrichmentService.enrichProjectsWithAllData(validProjects, mobile, combinedStats);

        // 构建用户画像DTO
        UserProfileDto userProfileDto = new UserProfileDto();
        userProfileDto.setMobile(mobile);
        userProfileDto.setProjects(validProjects);
        userProfileDto.setCombinedStats(combinedStats);

        // 分析用户画像标签
        userProfileDto.analyzeUserProfileTags(redisTemplate);

        // 生成用户画像的明细信息，包括项目明细，标签，项目周边分析
        // TODO: 通过角色权限来决定是否显示 detail
        userProfileDto.generateProjectDetail(false);

        // 使用Redis缓存结果
        try {
            String cacheKey = "user_profile:" + mobile;
            // 这里可以添加缓存逻辑
            redisTemplate.opsForValue().set(cacheKey, "cached", java.time.Duration.ofMinutes(30));
        } catch (Exception e) {
            log.warn("缓存用户画像数据失败: {}", e.getMessage());
        }

        log.info("用户画像查询完成，手机号: {}, 项目数量: {}", MaskUtil.maskMobile(mobile), validProjects.size());
        return userProfileDto;
    }

    @Override
    public Map<String, UserProfileDto> batchQueryUserProfile(List<String> mobiles, boolean showDetail) {
        log.info("开始批量查询用户画像，手机号数量: {}", mobiles.size());
        long startTime = System.currentTimeMillis();

        Map<String, UserProfileDto> resultMap = new HashMap<>();

        // 使用并行流处理批量查询
        List<CompletableFuture<Void>> futures = mobiles.stream()
                .map(mobile -> CompletableFuture.runAsync(() -> {
                    try {
                        log.debug("开始查询用户画像，手机号: {}", MaskUtil.maskMobile(mobile));

                        // 从手机号获取有效的项目列表和原始统计数据
                        ProjectsQueryResult queryResult = projectDataQueryService.getValidProjectsFromMobile(mobile);
                        List<ProjectDetail> validProjects = queryResult.getProjects();
                        MobileCombinedStatsDTO combinedStats = queryResult.getCombinedStats();

                        // 为项目丰富所有数据
                        projectEnrichmentService.enrichProjectsWithAllData(validProjects, mobile, combinedStats);

                        // 构建用户画像DTO
                        UserProfileDto userProfileDto = new UserProfileDto();
                        userProfileDto.setMobile(mobile);
                        userProfileDto.setProjects(validProjects);
                        userProfileDto.setCombinedStats(combinedStats);

                        // 分析用户画像标签
                        userProfileDto.analyzeUserProfileTags(redisTemplate);

                        // 生成用户画像的明细信息
                        userProfileDto.generateProjectDetail(showDetail);

                        // 使用Redis缓存结果
                        try {
                            String cacheKey = "user_profile:" + mobile;
                            redisTemplate.opsForValue().set(cacheKey, "cached", java.time.Duration.ofMinutes(30));
                        } catch (Exception e) {
                            log.warn("缓存用户画像数据失败，手机号: {}, 错误: {}", MaskUtil.maskMobile(mobile), e.getMessage());
                        }

                        synchronized (resultMap) {
                            resultMap.put(mobile, userProfileDto);
                        }

                        log.debug("用户画像查询完成，手机号: {}, 项目数量: {}", MaskUtil.maskMobile(mobile), validProjects.size());
                    } catch (Exception e) {
                        log.error("查询用户画像失败，手机号: {}, 错误: {}", MaskUtil.maskMobile(mobile), e.getMessage(), e);
                        // 创建一个空的用户画像DTO表示查询失败
                        UserProfileDto errorDto = new UserProfileDto();
                        errorDto.setMobile(mobile);
                        synchronized (resultMap) {
                            resultMap.put(mobile, errorDto);
                        }
                    }
                }, batchQueryExecutor))
                .toList();

        // 等待所有查询完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        long endTime = System.currentTimeMillis();
        log.info("批量查询用户画像完成，手机号数量: {}, 成功数量: {}, 总耗时: {}ms",
                mobiles.size(), resultMap.size(), (endTime - startTime));

        return resultMap;
    }

    /**
     * 服务销毁时关闭线程池，防止资源泄漏
     */
    @PreDestroy
    public void destroy() {
        log.info("正在关闭用户画像服务的线程池...");
        if (batchQueryExecutor != null && !batchQueryExecutor.isShutdown()) {
            batchQueryExecutor.shutdown();
            try {
                // 等待正在执行的任务完成，最多等待30秒
                if (!batchQueryExecutor.awaitTermination(30, java.util.concurrent.TimeUnit.SECONDS)) {
                    log.warn("线程池未能在30秒内正常关闭，强制关闭");
                    batchQueryExecutor.shutdownNow();
                } else {
                    log.info("用户画像服务线程池已成功关闭");
                }
            } catch (InterruptedException e) {
                log.warn("等待线程池关闭时被中断", e);
                batchQueryExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }
}
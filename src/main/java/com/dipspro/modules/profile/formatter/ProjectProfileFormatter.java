package com.dipspro.modules.profile.formatter;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;

import com.dipspro.modules.chat.dto.CollapsibleContent;
import com.dipspro.modules.chat.dto.MessageContent;
import com.dipspro.modules.chat.dto.SuggestionItem;
import com.dipspro.modules.profile.dto.ProjectDetail;
import com.dipspro.modules.profile.dto.ProjectProfileDto;

import lombok.extern.slf4j.Slf4j;

/**
 * 楼盘画像格式化器
 * 
 * <AUTHOR>
 *         2025/5/11 08:30
 * @apiNote 用于格式化楼盘画像信息为对话内容
 */
@Slf4j
public class ProjectProfileFormatter {

    private final ProjectProfileDto projectProfile;

    public ProjectProfileFormatter(ProjectProfileDto projectProfile) {
        this.projectProfile = projectProfile;
    }

    /**
     * 格式化楼盘画像为MessageContent列表
     * 
     * @return 格式化后的消息内容列表
     */
    public List<MessageContent> formatProjectProfile() {
        List<MessageContent> contents = new ArrayList<>();

        // 根据查询状态生成不同的内容
        switch (projectProfile.getQueryStatus()) {
            case "exact":
                contents.addAll(formatExactMatchProfile());
                break;
            case "fuzzy":
                contents.addAll(formatFuzzyMatchProfile());
                break;
            case "multiple":
                contents.addAll(formatMultipleResultsProfile());
                break;
            case "not_found":
                contents.addAll(formatNotFoundProfile());
                break;
            default:
                contents.add(MessageContent.text("查询状态未知"));
        }

        return contents;
    }

    /**
     * 格式化精确匹配的楼盘画像
     * 
     * @return 消息内容列表
     */
    private List<MessageContent> formatExactMatchProfile() {
        List<MessageContent> contents = new ArrayList<>();

        // TODO: 实现精确匹配楼盘画像的格式化逻辑
        // 包含：项目详情、周边配套、价格信息、地理位置、周边楼盘、客户评价等
        contents.add(MessageContent.text("#### " + projectProfile.getProjectName()));

        // 项目基本信息
        if (projectProfile.getProjectDetail() != null) {
            contents.addAll(formatProjectBasicInfo());
        }

        return contents;
    }

    /**
     * 格式化模糊匹配的楼盘画像
     * 
     * @return 消息内容列表
     */
    private List<MessageContent> formatFuzzyMatchProfile() {
        List<MessageContent> contents = new ArrayList<>();

        // TODO: 实现模糊匹配楼盘画像的格式化逻辑
        contents.add(MessageContent.text("#### 楼盘画像（模糊匹配）：" + projectProfile.getProjectName()));
        contents.add(MessageContent.text("*注：通过模糊匹配找到的楼盘信息*"));

        // 复用精确匹配的格式化逻辑
        contents.addAll(formatExactMatchProfile().subList(1, formatExactMatchProfile().size()));

        return contents;
    }

    /**
     * 格式化多个匹配结果
     * 
     * @return 消息内容列表
     */
    private List<MessageContent> formatMultipleResultsProfile() {
        List<MessageContent> contents = new ArrayList<>();

        contents.add(MessageContent.text("#### 找到多个匹配的楼盘"));
        contents.add(MessageContent.text("请从以下选项中选择您要查询的楼盘："));

        // 直接使用已经构建好的建议选项
        List<SuggestionItem> suggestionItems = projectProfile.getSuggestionItems();
        if (suggestionItems != null && !suggestionItems.isEmpty()) {
            contents.add(MessageContent.suggestion(suggestionItems.toArray(new SuggestionItem[0])));
        }

        return contents;
    }

    /**
     * 格式化未找到结果
     * 
     * @return 消息内容列表
     */
    private List<MessageContent> formatNotFoundProfile() {
        List<MessageContent> contents = new ArrayList<>();

        // TODO: 实现未找到结果的格式化逻辑
        contents.add(MessageContent.text("#### 未找到楼盘信息"));
        contents.add(MessageContent.text("抱歉，没有找到名为「" + projectProfile.getProjectName() + "」的楼盘信息。"));
        contents.add(MessageContent.text("请检查楼盘名称是否正确，或尝试使用其他关键词搜索。"));

        return contents;
    }

    /**
     * 格式化项目基本信息
     * 
     * @return 消息内容列表
     */
    private List<MessageContent> formatProjectBasicInfo() {
        List<MessageContent> contents = new ArrayList<>();

        // 包含：楼盘档次、房屋类型、装修类型、价格、物业费等
        ProjectDetail detail = projectProfile.getProjectDetail();
        if (detail != null) {
            // CollapsibleContent basicInfo = CollapsibleContent.withSummary("项目基本信息");

            // 构建基本信息文本
            List<String> infoOptions = new ArrayList<>();
            List<String> anaOptions = new ArrayList<>();
            List<String> options = new ArrayList<>();

            // 地理位置
            if (StringUtils.isNotBlank(detail.getAddress())) {
                infoOptions.add("位于" + detail.getAddress());
            } else if (StringUtils.isNotBlank(detail.getCity()) || StringUtils.isNotBlank(detail.getDistrict())) {
                String location = "";
                if (StringUtils.isNotBlank(detail.getCity())) {
                    location += detail.getCity();
                }
                if (StringUtils.isNotBlank(detail.getDistrict())) {
                    if (!location.isEmpty()) {
                        location += " ";
                    }
                    location += detail.getDistrict();
                }
                if (!location.isEmpty()) {
                    infoOptions.add("位于" + location);
                }
            }

            // 开发商
            // 来源网络（或者lj_matching中customer_name或lj_loupan中brand_name，优先用mathcing表）
            if (StringUtils.isNotBlank(detail.getCustomerName())) {
                infoOptions.add("由" + detail.getCustomerName() + "负责开发");
            } else if (StringUtils.isNotBlank(detail.getBranName())) {
                infoOptions.add("由" + detail.getBranName() + "负责开发");
            }

            // 房价
            // 来源网络（或者lj_matching中lj_loupan_price_average或lj_loupan中price_average，优先用matching表，问题同上）
            if (StringUtils.isNotBlank(detail.getLjLoupanPriceAverage())) {
                infoOptions.add("房价约" + detail.getLjLoupanPriceAverage());
            } else if (null != detail.getLjLouPan().get("price_average")) {
                String price = detail.getLjLouPan().get("price_average") + "";
                infoOptions.add("房价约" + price);
            }

            // 样框 - data_nameList_1_24中，count该项目d_year=2024的名单数量
            if (null != detail.getTotalSample()) {
                infoOptions.add("约：" + detail.getTotalSample() + "户");
            }

            // 业主阶段
            if (StringUtils.isNotBlank(detail.getOwnerStage())) {
                infoOptions.add("项目目前主要处于" + detail.getOwnerStage());
            }

            // 楼盘档次
            if (StringUtils.isNotBlank(detail.getBuildingGrade())) {
                infoOptions.add("楼盘档次：" + detail.getBuildingGrade());
            }

            // 满意度得分
            Map<String, Integer> indexScore = projectProfile.getProjectDetail().getIndexScore();
            if (null != indexScore && !indexScore.isEmpty()) {
                anaOptions.add("2024年");
                Integer ra1a5 = indexScore.get("ra1a5");
                Integer rt0a5 = indexScore.get("rt0a5");
                if (null != ra1a5) {
                    anaOptions.add("客户满意度得分：" + ra1a5 + "分");
                }
                if (null != rt0a5) {
                    anaOptions.add("物业服务满意度得分：" + rt0a5 + "分");
                }
            }

            // 将所有信息合并到一个detail中
            if (!infoOptions.isEmpty()) {
                options.add(String.join("，", infoOptions));
            }
            if (!anaOptions.isEmpty()) {
                options.add(String.join("，", anaOptions));
            }
            if (!options.isEmpty()) {
                // basicInfo.addExpandableDetail("详细信息", String.join("\n\n", options));
                contents.add(MessageContent.text(String.join("\n\n", options)));
            }

            // contents.add(MessageContent.collapsible(basicInfo));
        }

        return contents;
    }
}
package com.dipspro.modules.system.service.impl;

import com.dipspro.constant.StatusConstants;
import com.dipspro.modules.system.dto.NotificationDto;
import com.dipspro.modules.system.entity.Notification;
import com.dipspro.modules.system.repository.NotificationRepository;
import com.dipspro.modules.system.service.NotificationService;
import com.dipspro.modules.user.entity.User;
import com.dipspro.modules.user.repository.UserRepository;
import com.dipspro.util.SecurityUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-07-31
 **/
@Service
@Transactional
@Slf4j
public class NotificationServiceImpl implements NotificationService {

    @Autowired
    NotificationRepository notificationRepository;
    @Autowired
    UserRepository userRepository;

    @Override
    public List<NotificationDto> findByUserIdAndStatus(Long userId, String status) {
        List<Notification> notifications = notificationRepository.findByUserIdAndStatus(userId, status);
        return notifications.stream().map(this::convertToDto).collect(Collectors.toList());
    }

    private NotificationDto convertToDto(Notification notification) {
        NotificationDto dto = new NotificationDto();
        BeanUtils.copyProperties(notification, dto);
        return dto;
    }

    @Override
    public void readNotification(Long id) {
        Notification notification = notificationRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Notification 不存在，id=" + id));

        notification.setStatus(StatusConstants.READ);
        notification.setUpdateBy(SecurityUtil.getCurrentUsername());
        notification.setUpdateTime(new Timestamp(System.currentTimeMillis()));
        notificationRepository.save(notification);
    }

    @Override
    public void createNotification(Map<String, Object> params) {
        String usernames = params.get("usernames").toString();
        String type = params.get("type").toString();
        String title = params.get("title").toString();
        String details = params.get("details").toString();
        String path = params.get("path").toString();
        String createBy = params.get("createBy").toString();

        String[] usernameArray = usernames.split(",");
        List<User> users = userRepository.findByUsernames(usernameArray);
        if (users == null || users.isEmpty()) {
            log.error("createNotification: User 均不存在，usernames={}", usernames);
            return;
        }

        List<Notification> notifications = new ArrayList<>();
        for (User user : users) {
            Notification notification = new Notification();
            notification.setUserId(user.getId());
            notification.setType(type);
            notification.setTitle(title);
            notification.setDetails(details);
            notification.setPath(path);
            notification.setStatus(StatusConstants.UNREAD);
            notification.setCreateBy(createBy);
            notification.setCreateTime(new Timestamp(System.currentTimeMillis()));
            notifications.add(notification);
        }

        notificationRepository.saveAll(notifications);
    }
}

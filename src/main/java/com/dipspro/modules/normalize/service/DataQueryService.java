package com.dipspro.modules.normalize.service;

import java.util.List;

import com.dipspro.modules.chat.dto.MessageContent;
import com.dipspro.modules.normalize.dto.BatchUserProfileRequest;
import com.dipspro.modules.normalize.dto.ProjectProfileRequest;
import com.dipspro.modules.profile.dto.ProjectProfileDto;
import com.dipspro.modules.profile.dto.UserProfileDto;

/**
 * <AUTHOR>
 *         2025/5/11 08:15
 * @apiNote 数据查询接口
 */
public interface DataQueryService {

    UserProfileDto userProfile(String mobile);

    /**
     * 批量查询用户画像
     * 
     * @param request 批量查询请求参数
     * @return 批量查询结果
     */
    List<MessageContent> batchUserProfile(BatchUserProfileRequest request);

    /**
     * 根据楼盘画像请求查询楼盘画像
     * 
     * @param request 楼盘画像请求参数
     * @return 楼盘画像信息
     */
    ProjectProfileDto projectProfile(ProjectProfileRequest request);

    /**
     * 根据名称查询楼盘画像 - 获取楼盘的营销相关信息
     *
     * @param request 楼盘画像请求参数
     * @return 楼盘画像信息
     */
    ProjectProfileDto projectProfileSalesInfo(ProjectProfileRequest request);

    /**
     * 根据名称查询楼盘画像 - 了解楼盘周边直线距离 N 公里的其他楼盘情况
     *
     * @param request 楼盘画像请求参数
     * @return 楼盘画像信息
     */
    ProjectProfileDto projectProfileSurrounding(ProjectProfileRequest request);

    /**
     * 根据名称查询楼盘画像 - 了解楼盘时间范围、属性、指标得分
     *
     * @param request 楼盘画像请求参数
     * @return 楼盘画像信息
     */
    ProjectProfileDto projectProfileAna(ProjectProfileRequest request);
}

package com.dipspro.modules.profile.formatter;

import com.dipspro.modules.chat.dto.MessageContent;
import com.dipspro.modules.chat.dto.SlotDefinition;
import com.dipspro.modules.chat.dto.SuggestionItem;
import com.dipspro.modules.profile.dto.ProjectDetail;
import com.dipspro.modules.profile.dto.ProjectProfileDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 楼盘画像格式化器
 *
 * <AUTHOR>
 *         2025-06-09 13:42:20
 * @apiNote 用于格式化楼盘画像信息为对话内容
 */
@Slf4j
public class ProjectProfileAnaFormatter {

    private final ProjectProfileDto projectProfile;

    public ProjectProfileAnaFormatter(ProjectProfileDto projectProfile) {
        this.projectProfile = projectProfile;
    }

    /**
     * 格式化楼盘画像为MessageContent列表
     *
     * @return 格式化后的消息内容列表
     */
    public List<MessageContent> formatProjectProfile() {
        List<MessageContent> contents = new ArrayList<>();

        // 根据查询状态生成不同的内容
        switch (projectProfile.getQueryStatus()) {
            case "exact":
                contents.addAll(formatExactMatchProfile());
                break;
            case "fuzzy":
                contents.addAll(formatFuzzyMatchProfile());
                break;
            case "multiple":
                contents.addAll(formatMultipleResultsProfile());
                break;
            case "not_found":
                contents.addAll(formatNotFoundProfile());
                break;
            default:
                contents.add(MessageContent.text("查询状态未知"));
        }

        return contents;
    }

    /**
     * 格式化精确匹配的楼盘画像
     *
     * @return 消息内容列表
     */
    private List<MessageContent> formatExactMatchProfile() {
        return new ArrayList<>(format());
    }

    /**
     * 格式化模糊匹配的楼盘画像
     *
     * @return 消息内容列表
     */
    private List<MessageContent> formatFuzzyMatchProfile() {
        List<MessageContent> contents = new ArrayList<>();

        contents.add(MessageContent.text("#### 楼盘画像（模糊匹配）：" + projectProfile.getProjectName()));
        contents.add(MessageContent.text("*注：通过模糊匹配找到的楼盘信息*"));

        // 复用精确匹配的格式化逻辑
        contents.addAll(formatExactMatchProfile().subList(1, formatExactMatchProfile().size()));

        return contents;
    }

    /**
     * 格式化多个匹配结果
     *
     * @return 消息内容列表
     */
    private List<MessageContent> formatMultipleResultsProfile() {
        List<MessageContent> contents = new ArrayList<>();

        contents.add(MessageContent.text("#### 找到多个匹配的楼盘"));
        contents.add(MessageContent.text("请从以下选项中选择您要查询的楼盘："));

        // 直接使用已经构建好的建议选项
        List<SuggestionItem> suggestionItems = projectProfile.getSuggestionItems();
        if (suggestionItems != null && !suggestionItems.isEmpty()) {
            contents.add(MessageContent.suggestion(suggestionItems.toArray(new SuggestionItem[0])));
        }

        return contents;
    }

    /**
     * 格式化未找到结果
     *
     * @return 消息内容列表
     */
    private List<MessageContent> formatNotFoundProfile() {
        List<MessageContent> contents = new ArrayList<>();

        contents.add(MessageContent.text("#### 未找到楼盘信息"));
        contents.add(MessageContent.text("抱歉，没有找到名为「" + projectProfile.getProjectName() + "」的楼盘信息。"));
        contents.add(MessageContent.text("请检查楼盘名称是否正确，或尝试使用其他关键词搜索。"));

        return contents;
    }

    /**
     * 格式化得分信息
     *
     * @return 消息内容列表
     */
    private List<MessageContent> format() {
        List<MessageContent> contents = new ArrayList<>();

        ProjectDetail projectDetail = projectProfile.getProjectDetail();
        /*
        [北京保利中央公馆][2025年1-5月][准业主]的[忠诚度]为[80]，样本量共计[30]个；[准业主]的[销售服务]评价为[98]，样本量共计[29]个。
         */
        List<String> lines = new ArrayList<>();

        Map<String, Integer> indexScore = projectDetail.getIndexScore();
        if (!indexScore.isEmpty()) {
            Map<String, Object> slots = projectProfile.getSlots();
            String projectName = slots.get("project_name") + "";
            String dataPeriod = slots.get("data_period") + "";
            String attribute = slots.get("attribute") + "";
            String indexName = slots.get("index_name") + "";

            String indexDesc = indexName;
            for (SlotDefinition slotDefinition : projectProfile.getSlotDefinitions()) {
                if (!slotDefinition.getName().equals("index_name")) continue;

                for (SlotDefinition.Option option : slotDefinition.getOptions()) {
                    if (option.getValue().equalsIgnoreCase(indexName)) {
                        indexDesc = option.getLabel();
                    }
                }
            }

            lines.add(String.format("%s%s%s的%s为%d，样本量共计%d个"
                    , projectName, dataPeriod, attribute, indexDesc, indexScore.get(indexName), indexScore.get("total_sample")));
        }

        if (lines.isEmpty()) {
            contents.add(MessageContent.text("没有查询到相关得分信息，您可以尝试更换其他条件查询"));
        } else {
            contents.add(MessageContent.text(String.join("。\n", lines)));
        }

        return contents;
    }

    private String str(String text) {
        if (StringUtils.isBlank(text))
            return "";
        return text;
    }

}
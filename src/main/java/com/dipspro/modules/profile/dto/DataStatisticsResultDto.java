package com.dipspro.modules.profile.dto;

import java.util.ArrayList;
import java.util.List;

import com.dipspro.modules.normalize.dto.NormalizeData;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 数据统计结果DTO，包含原始数据和按项目分组的统计结果
 */
@Data
@NoArgsConstructor
public class DataStatisticsResultDto {
    
    /**
     * 原始数据记录列表
     */
    private List<NormalizeData> originalRecords = new ArrayList<>();
    
    /**
     * 按项目分组的统计结果
     */
    private List<ProjectStatisticsDto> projectStatistics = new ArrayList<>();
    
    /**
     * 总记录数
     */
    private int totalRecords;
    
    /**
     * 项目数
     */
    private int projectCount;
    
    /**
     * 数据来源描述
     */
    private String dataSourceDescription;
    
    public DataStatisticsResultDto(List<NormalizeData> originalRecords, String dataSourceDescription) {
        this.originalRecords = originalRecords;
        this.totalRecords = originalRecords.size();
        this.dataSourceDescription = dataSourceDescription;
    }
    
    public void setOriginalRecords(List<NormalizeData> originalRecords) {
        this.originalRecords = originalRecords;
        this.totalRecords = originalRecords.size();
    }
    
    public void setProjectStatistics(List<ProjectStatisticsDto> projectStatistics) {
        this.projectStatistics = projectStatistics;
        this.projectCount = projectStatistics.size();
    }
} 
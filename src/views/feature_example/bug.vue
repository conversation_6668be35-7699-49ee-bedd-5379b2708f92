<route lang="yaml">
meta:
  enabled: false
</route>

<script setup lang="ts">
import useSettingsStore from '@/store/modules/settings'

const settingsStore = useSettingsStore()

const env = import.meta.env.MODE

const errorLog = ref('')

const errorLogInfo = computed(() => {
  return errorLog.value ? [JSON.parse(errorLog.value)] : []
})

function bugTest(type: any) {
  nextTick(() => {
    errorLog.value = sessionStorage.getItem('errorLog') || ''
  })
  switch (type) {
    case 1:
      // @ts-expect-error: ignore
      a = abc
      break
    case 2:
      // @ts-expect-error: ignore
      testMethod()
      break
  }
}
function open(url: string) {
  window.open(url, '_blank')
}
</script>

<template>
  <div>
    <FaPageHeader title="错误日志" description="错误日志通过 Vue 提供的全局错误钩子 errorHandler 进行拦截，如果需要上报给后端，需自行实现">
      <ElButton @click="open('https://cn.vuejs.org/api/application.html#app-config-errorhandler')">
        <template #icon>
          <FaIcon name="i-ep:link" />
        </template>
        Vue errorHandler 说明
      </ElButton>
    </FaPageHeader>
    <FaPageMain>
      <div v-if="env !== 'development'">
        <div v-if="!settingsStore.settings.app.enableErrorLog">
          请到 /src/settings.ts 里打开错误日志功能，再进入该页面查看演示
        </div>
        <div v-else>
          <ElButton type="danger" @click="bugTest(1)">
            模拟触发错误1
          </ElButton>
          <ElButton type="danger" @click="bugTest(2)">
            模拟触发错误2
          </ElButton>
          <ElTable :data="errorLogInfo" border>
            <ElTableColumn label="错误信息" width="200" align="center">
              <template #default="scope">
                <div>{{ scope.row.err.message }}</div>
                <ElTag type="danger">
                  {{ scope.row.info }}
                </ElTag>
              </template>
            </ElTableColumn>
            <ElTableColumn label="错误详情">
              <template #default="scope">
                <div style="white-space: pre-wrap;">
                  {{ scope.row.err.stack }}
                </div>
              </template>
            </ElTableColumn>
            <ElTableColumn label="错误链接" width="200" align="center">
              <template #default="scope">
                <ElLink :href="scope.row.url" target="_blank">
                  {{ scope.row.url }}
                </ElLink>
              </template>
            </ElTableColumn>
            <ElTableColumn prop="datetime" label="时间" width="200" align="center" />
          </ElTable>
        </div>
      </div>
      <div v-else>
        当前为开发环境，该功能关闭演示
      </div>
    </FaPageMain>
  </div>
</template>

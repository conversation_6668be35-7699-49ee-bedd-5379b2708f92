package com.dipspro.modules.profile.service;

import java.util.List;
import java.util.Map;

import com.dipspro.modules.profile.dto.UserProfileDto;

/**
 * 用户画像服务接口
 * 
 * <AUTHOR>
 * @since 2025-06-03 14:51:45
 */
public interface UserProfileService {

    /**
     * 根据手机号查询用户画像
     * 
     * @param mobile 手机号
     * @return 用户画像信息
     */
    UserProfileDto queryUserProfile(String mobile);

    /**
     * 批量查询用户画像
     * 
     * @param mobiles    手机号列表
     * @param showDetail 是否显示详细信息
     * @return 手机号到用户画像的映射，key为手机号，value为用户画像信息
     */
    Map<String, UserProfileDto> batchQueryUserProfile(List<String> mobiles, boolean showDetail);
}
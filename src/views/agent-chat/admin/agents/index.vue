<template>
  <div class="agent-management-page" :class="{ testing: isAnyAgentTesting }">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">
          <i class="i-carbon-machine-learning"></i>
          AI Agent 管理
        </h1>
        <div class="page-description">
          管理和配置 AI Agent，设置智能协作流程
        </div>
      </div>

      <div class="header-actions">
        <button
          @click="showImportDialog = true"
          class="btn-secondary"
          :disabled="loading"
        >
          <i class="i-carbon-upload"></i>
          导入配置
        </button>
        <button
          @click="createAgent"
          class="btn-primary"
          :disabled="loading"
        >
          <i class="i-carbon-add"></i>
          创建 Agent
        </button>
      </div>
    </div>

    <!-- 统计概览 -->
    <div class="stats-overview">
      <MetricCard
        title="总计"
        :value="agentStatistics.total"
        icon="i-carbon-robot"
        color="primary"
        :trend="10"
        trend-label="较上月"
        :loading="loading"
      />
      <MetricCard
        title="启用中"
        :value="agentStatistics.active"
        icon="i-carbon-circle-filled"
        color="success"
        :trend="5"
        trend-label="较上月"
        :loading="loading"
      />
      <MetricCard
        title="禁用"
        :value="agentStatistics.inactive"
        icon="i-carbon-circle-dash"
        color="warning"
        :trend="-2"
        trend-label="较上月"
        :loading="loading"
      />
      <MetricCard
        title="草稿"
        :value="agentStatistics.draft"
        icon="i-carbon-document-draft"
        color="info"
        :trend="3"
        trend-label="较上月"
        :loading="loading"
      />
    </div>

    <!-- 过滤器和搜索 -->
    <div class="filter-bar">
      <div class="search-container">
        <div class="search-input-wrapper">
          <i class="i-carbon-search search-icon"></i>
          <input
            v-model="searchQuery"
            type="text"
            placeholder="搜索 Agent 名称或描述..."
            class="search-input"
            @input="debouncedSearch"
          >
          <button
            v-if="searchQuery"
            @click="clearSearch"
            class="clear-search-btn"
          >
            <i class="i-carbon-close"></i>
          </button>
        </div>
      </div>

      <div class="filter-controls">
        <div class="filter-group">
          <label class="filter-label">状态</label>
          <select v-model="statusFilter" class="filter-select" @change="applyFilters">
            <option value="">所有状态</option>
            <option value="ACTIVE">启用</option>
            <option value="INACTIVE">禁用</option>
            <option value="DRAFT">草稿</option>
          </select>
        </div>

        <div class="filter-group">
          <label class="filter-label">类别</label>
          <select v-model="categoryFilter" class="filter-select" @change="applyFilters">
            <option value="">所有类别</option>
            <option value="ANALYSIS">数据分析</option>
            <option value="PROCESSING">数据处理</option>
            <option value="INFERENCE">推理</option>
            <option value="REPORTING">报告生成</option>
            <option value="VISUALIZATION">可视化</option>
          </select>
        </div>

        <button
          @click="resetFilters"
          class="reset-btn"
          :disabled="!hasActiveFilters"
        >
          <i class="i-carbon-filter-reset"></i>
          重置
        </button>
      </div>
    </div>

    <!-- Agent 列表 -->
    <div class="agent-list-container">
      <div class="list-header">
        <div class="list-info">
          <span class="result-count">
            共 {{ filteredAgents.length }} 个 Agent
            <span v-if="hasActiveFilters" class="filter-indicator">
              (已筛选)
            </span>
          </span>
          <div v-if="selectedAgents.length > 0" class="selection-info">
            已选择 {{ selectedAgents.length }} 个
          </div>
        </div>

        <div class="view-controls">
          <div class="view-mode-switcher">
            <button
              @click="viewMode = 'grid'"
              :class="{ active: viewMode === 'grid' }"
              class="view-btn"
              title="网格视图"
            >
              <i class="i-carbon-grid"></i>
            </button>
            <button
              @click="viewMode = 'list'"
              :class="{ active: viewMode === 'list' }"
              class="view-btn"
              title="列表视图"
            >
              <i class="i-carbon-list"></i>
            </button>
          </div>

          <div class="sort-controls">
            <label class="sort-label">排序</label>
            <select v-model="sortField" class="sort-select" @change="applySorting">
              <option value="updatedAt">更新时间</option>
              <option value="createdAt">创建时间</option>
              <option value="name">名称</option>
              <option value="priority">优先级</option>
            </select>
            <button
              @click="toggleSortOrder"
              class="sort-order-btn"
              :title="sortOrder === 'desc' ? '降序' : '升序'"
            >
              <i :class="sortOrder === 'desc' ? 'i-carbon-sort-descending' : 'i-carbon-sort-ascending'"></i>
            </button>
          </div>
        </div>
      </div>

      <!-- 网格视图 -->
      <div v-if="viewMode === 'grid'" class="agent-grid">
        <!-- 加载状态 -->
        <div v-if="loading" class="loading-grid">
          <div
            v-for="i in 6"
            :key="i"
            class="loading-card"
          >
            <div class="loading-skeleton"></div>
          </div>
        </div>

        <!-- Agent 卡片 -->
        <AgentCard
          v-for="agent in paginatedAgents"
          :key="agent.id"
          :agent="agent"
          @edit="editAgent"
          @duplicate="duplicateAgent"
          @delete="confirmDeleteAgent"
          @toggle-status="toggleAgentStatus"
          @test-start="handleTestStart"
          @test-end="handleTestEnd"
        />

        <!-- 空状态 -->
        <div v-if="!loading && filteredAgents.length === 0" class="empty-state">
          <div class="empty-content">
            <i class="i-carbon-robot empty-icon"></i>
            <h3>{{ getEmptyStateTitle() }}</h3>
            <p>{{ getEmptyStateMessage() }}</p>
            <div class="empty-actions">
              <button v-if="hasActiveFilters" @click="resetFilters" class="btn-secondary">
                清除筛选
              </button>
              <button @click="createAgent" class="btn-primary">
                创建 Agent
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 列表视图 -->
      <div v-else class="agent-table-container">
        <AgentTable
          :agents="paginatedAgents"
          :loading="loading"
          :sort-field="sortField"
          :sort-order="sortOrder"
          @edit="editAgent"
          @delete="confirmDeleteAgent"
          @duplicate="duplicateAgent"
          @toggle-status="toggleAgentStatus"
          @sort="handleSort"
          @bulk-action="handleBulkAction"
          @test-start="handleTestStart"
          @test-end="handleTestEnd"
        />
      </div>

      <!-- 分页 -->
      <div v-if="!loading && filteredAgents.length > pageSize" class="pagination-container">
        <div class="pagination-info">
          显示 {{ paginationStart }}-{{ paginationEnd }} 条，共 {{ filteredAgents.length }} 条
        </div>

        <div class="pagination-controls">
          <button
            @click="currentPage = 1"
            :disabled="currentPage === 1"
            class="pagination-btn"
          >
            <i class="i-carbon-chevron-left-double"></i>
          </button>
          <button
            @click="currentPage--"
            :disabled="currentPage === 1"
            class="pagination-btn"
          >
            <i class="i-carbon-chevron-left"></i>
          </button>

          <div class="page-numbers">
            <button
              v-for="page in visiblePages"
              :key="page"
              @click="typeof page === 'number' ? currentPage = page : undefined"
              :class="{ active: currentPage === page }"
              class="page-btn"
              :disabled="typeof page !== 'number'"
            >
              {{ page }}
            </button>
          </div>

          <button
            @click="currentPage++"
            :disabled="currentPage === totalPages"
            class="pagination-btn"
          >
            <i class="i-carbon-chevron-right"></i>
          </button>
          <button
            @click="currentPage = totalPages"
            :disabled="currentPage === totalPages"
            class="pagination-btn"
          >
            <i class="i-carbon-chevron-right-double"></i>
          </button>
        </div>

        <div class="page-size-selector">
          <label>每页</label>
          <select v-model="pageSize" @change="handlePageSizeChange">
            <option :value="10">10</option>
            <option :value="20">20</option>
            <option :value="50">50</option>
            <option :value="100">100</option>
          </select>
          <span>条</span>
        </div>
      </div>
    </div>

    <!-- Agent 编辑对话框 -->
    <AgentEditDialog
      v-model:visible="editDialogVisible"
      :agent="editingAgent"
      :mode="editMode"
      @save="saveAgent"
      @cancel="cancelEdit"
    />

    <!-- 导入对话框 -->
    <ImportDialog
      v-model:visible="showImportDialog"
      @import="handleImport"
    />

    <!-- 删除确认对话框 -->
    <div v-if="deleteDialog.visible" class="dialog-overlay">
      <div class="dialog">
        <div class="dialog-header">
          <h4>确认删除</h4>
          <button @click="closeDeleteDialog" class="dialog-close">
            <i class="i-carbon-close"></i>
          </button>
        </div>
        <div class="dialog-content">
          <p>确定要删除 Agent "{{ deleteDialog.agent?.name }}" 吗？</p>
          <p class="warning-text">此操作不可撤销，相关的流程配置也会受到影响。</p>
        </div>
        <div class="dialog-actions">
          <button @click="closeDeleteDialog" class="btn-secondary">
            取消
          </button>
          <button @click="confirmDelete" class="btn-danger" :disabled="deleting">
            <i v-if="deleting" class="i-carbon-in-progress animate-spin"></i>
            {{ deleting ? '删除中...' : '确认删除' }}
          </button>
        </div>
      </div>
    </div>

    <!-- 通知提示 -->
    <div v-if="notification.visible" class="notification" :class="notification.type">
      <i :class="getNotificationIcon()"></i>
      <span>{{ notification.message }}</span>
      <button @click="hideNotification" class="notification-close">
        <i class="i-carbon-close"></i>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, nextTick } from 'vue'
import { useAgentManagementStore } from '@/stores/modules/agentManagement'
import AgentCard from '@/components/agent-chat/admin/AgentCard.vue'
import AgentTable from '@/components/agent-chat/admin/AgentTable.vue'
import MetricCard from '@/components/agent-chat/admin/MetricCard.vue'
import AgentEditDialog from '@/components/agent-chat/admin/AgentEditDialog.vue'
import ImportDialog from '@/components/agent-chat/admin/ImportDialog.vue'
import type { AgentDefinition } from '@/api/agentManagement'

// Store
const agentStore = useAgentManagementStore()

// 响应式数据
const viewMode = ref<'grid' | 'list'>('grid')
const searchQuery = ref('')
const statusFilter = ref('')
const categoryFilter = ref('')
const sortField = ref('updatedAt')
const sortOrder = ref<'asc' | 'desc'>('desc')
const currentPage = ref(1)
const pageSize = ref(20)

// 测试状态管理
const testingAgents = ref<number[]>([])
const isAnyAgentTesting = computed(() => testingAgents.value.length > 0)

const editDialogVisible = ref(false)
const showImportDialog = ref(false)
const editingAgent = ref<AgentDefinition | null>(null)
const editMode = ref<'create' | 'edit'>('create')
const selectedAgents = ref<AgentDefinition[]>([])

const deleteDialog = ref({
  visible: false,
  agent: null as AgentDefinition | null
})
const deleting = ref(false)

const notification = ref({
  visible: false,
  message: '',
  type: 'info' as 'success' | 'error' | 'warning' | 'info'
})

// 计算属性
const agents = computed(() => agentStore.agents)
const loading = computed(() => agentStore.agentLoading)
const error = computed(() => agentStore.agentError)
const agentStatistics = computed(() => agentStore.agentStatistics)

const hasActiveFilters = computed(() =>
  searchQuery.value || statusFilter.value || categoryFilter.value
)

const filteredAgents = computed(() => {
  let result = agents.value

  // 搜索过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(agent =>
      agent.name.toLowerCase().includes(query) ||
      agent.description?.toLowerCase().includes(query)
    )
  }

  // 状态过滤
  if (statusFilter.value) {
    result = result.filter(agent => agent.status === statusFilter.value)
  }

  // 类别过滤
  if (categoryFilter.value) {
    result = result.filter(agent => agent.category === categoryFilter.value)
  }

  // 排序
  result.sort((a, b) => {
    const aValue = (a as any)[sortField.value]
    const bValue = (b as any)[sortField.value]

    if (sortOrder.value === 'asc') {
      return aValue > bValue ? 1 : -1
    } else {
      return aValue < bValue ? 1 : -1
    }
  })

  return result
})

const totalPages = computed(() => Math.ceil(filteredAgents.value.length / pageSize.value))

const paginatedAgents = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredAgents.value.slice(start, end)
})

const paginationStart = computed(() =>
  filteredAgents.value.length === 0 ? 0 : (currentPage.value - 1) * pageSize.value + 1
)

const paginationEnd = computed(() =>
  Math.min(currentPage.value * pageSize.value, filteredAgents.value.length)
)

const visiblePages = computed(() => {
  const delta = 2
  const range = []
  const rangeWithDots = []

  for (let i = Math.max(2, currentPage.value - delta);
       i <= Math.min(totalPages.value - 1, currentPage.value + delta);
       i++) {
    range.push(i)
  }

  if (currentPage.value - delta > 2) {
    rangeWithDots.push(1, '...')
  } else {
    rangeWithDots.push(1)
  }

  rangeWithDots.push(...range)

  if (currentPage.value + delta < totalPages.value - 1) {
    rangeWithDots.push('...', totalPages.value)
  } else if (totalPages.value > 1) {
    rangeWithDots.push(totalPages.value)
  }

  return rangeWithDots.filter((item, index, self) => self.indexOf(item) === index)
})

// 防抖搜索
let searchTimeout: ReturnType<typeof setTimeout> | undefined
const debouncedSearch = () => {
  clearTimeout(searchTimeout)
  searchTimeout = setTimeout(() => {
    currentPage.value = 1
    applyFilters()
  }, 300)
}

// 生命周期
onMounted(async () => {
  await loadAgents()

  // 监听错误
  watch(error, (newError) => {
    if (newError) {
      showNotification(newError, 'error')
      agentStore.clearAgentError()
    }
  })
})

// 监听筛选变化
watch([statusFilter, categoryFilter], () => {
  currentPage.value = 1
})

// 方法
async function loadAgents() {
  try {
    await agentStore.loadAgents()
  } catch (error) {
    console.error('Failed to load agents:', error)
  }
}

function applyFilters() {
  agentStore.setAgentFilters({
    status: statusFilter.value,
    category: categoryFilter.value,
    search: searchQuery.value
  })
  currentPage.value = 1
}

function resetFilters() {
  searchQuery.value = ''
  statusFilter.value = ''
  categoryFilter.value = ''
  currentPage.value = 1
  agentStore.resetFilters()
}

function clearSearch() {
  searchQuery.value = ''
  applyFilters()
}

function applySorting() {
  // 排序逻辑已经在 filteredAgents 计算属性中处理，无需额外调用store方法
  // agentStore.sortAgents(sortField.value, sortOrder.value)
}

function toggleSortOrder() {
  sortOrder.value = sortOrder.value === 'desc' ? 'asc' : 'desc'
  // 不需要调用applySorting，计算属性会自动更新
}

function handleSort(field: string, order: 'asc' | 'desc') {
  sortField.value = field
  sortOrder.value = order
  // 不需要调用applySorting，计算属性会自动更新
}

function handlePageSizeChange() {
  currentPage.value = 1
}

function createAgent() {
  editingAgent.value = null
  editMode.value = 'create'
  editDialogVisible.value = true
}

function editAgent(agent: AgentDefinition) {
  editingAgent.value = { ...agent }
  editMode.value = 'edit'
  editDialogVisible.value = true
}

async function duplicateAgent(agent: AgentDefinition) {
  try {
    const duplicated = {
      ...agent,
      id: undefined,
      name: `${agent.name} (副本)`,
      status: 'DRAFT' as const
    }

    await agentStore.createAgent(duplicated)
    showNotification('Agent 复制成功', 'success')
  } catch (error) {
    showNotification('复制 Agent 失败', 'error')
  }
}

function confirmDeleteAgent(agent: AgentDefinition) {
  deleteDialog.value = {
    visible: true,
    agent
  }
}

async function confirmDelete() {
  if (!deleteDialog.value.agent) return

  try {
    deleting.value = true
    await agentStore.deleteAgent(deleteDialog.value.agent.id!)
    showNotification('Agent 删除成功', 'success')
    closeDeleteDialog()
  } catch (error) {
    showNotification('删除 Agent 失败', 'error')
  } finally {
    deleting.value = false
  }
}

function closeDeleteDialog() {
  deleteDialog.value.visible = false
  deleteDialog.value.agent = null
}

async function toggleAgentStatus(agent: AgentDefinition) {
  try {
    const newStatus = agent.status === 'ACTIVE' ? 'INACTIVE' : 'ACTIVE'
    await agentStore.updateAgentStatus(agent.id!, newStatus)
    showNotification(`Agent ${newStatus === 'ACTIVE' ? '启用' : '禁用'}成功`, 'success')
  } catch (error) {
    showNotification('更新 Agent 状态失败', 'error')
  }
}

async function saveAgent(agentData: AgentDefinition) {
  try {
    if (editMode.value === 'create') {
      await agentStore.createAgent(agentData)
      showNotification('Agent 创建成功', 'success')
    } else {
      await agentStore.updateAgent(agentData)
      showNotification('Agent 更新成功', 'success')
    }

    editDialogVisible.value = false
    editingAgent.value = null
  } catch (error) {
    showNotification(`${editMode.value === 'create' ? '创建' : '更新'} Agent 失败`, 'error')
  }
}

function cancelEdit() {
  editDialogVisible.value = false
  editingAgent.value = null
}

async function handleImport(data: any) {
  try {
    await agentStore.importAgents(data)
    showNotification('Agent 导入成功', 'success')
    showImportDialog.value = false
  } catch (error) {
    showNotification('导入 Agent 失败', 'error')
  }
}

async function handleBulkAction(action: string, agents: AgentDefinition[]) {
  try {
    switch (action) {
      case 'enable':
        // 批量启用逻辑
        showNotification(`已启用 ${agents.length} 个 Agent`, 'success')
        break
      case 'disable':
        // 批量禁用逻辑
        showNotification(`已禁用 ${agents.length} 个 Agent`, 'success')
        break
      case 'delete':
        // 批量删除逻辑
        showNotification(`已删除 ${agents.length} 个 Agent`, 'success')
        break
    }
  } catch (error) {
    showNotification('批量操作失败', 'error')
  }
}

function getEmptyStateTitle(): string {
  if (searchQuery.value) return '无搜索结果'
  if (statusFilter.value || categoryFilter.value) return '无符合条件的 Agent'
  return '暂无 Agent'
}

function getEmptyStateMessage(): string {
  if (searchQuery.value) return `未找到包含 "${searchQuery.value}" 的 Agent`
  if (statusFilter.value || categoryFilter.value) return '请尝试调整筛选条件'
  return '还没有创建任何 Agent，点击按钮开始创建'
}

let notificationTimer: ReturnType<typeof setTimeout> | null = null

function showNotification(message: string, type: typeof notification.value.type = 'info') {
  // 清除之前的定时器
  if (notificationTimer) {
    clearTimeout(notificationTimer)
    notificationTimer = null
  }

  notification.value = {
    visible: true,
    message,
    type
  }

  notificationTimer = setTimeout(() => {
    hideNotification()
  }, 4000)
}

function hideNotification() {
  if (notificationTimer) {
    clearTimeout(notificationTimer)
    notificationTimer = null
  }
  notification.value.visible = false
}

function getNotificationIcon(): string {
  switch (notification.value.type) {
    case 'success': return 'i-carbon-checkmark-filled'
    case 'error': return 'i-carbon-error-filled'
    case 'warning': return 'i-carbon-warning-filled'
    default: return 'i-carbon-information-filled'
  }
}

// 测试状态管理
function handleTestStart(agentId: number) {
  if (!testingAgents.value.includes(agentId)) {
    testingAgents.value.push(agentId)
  }
}

function handleTestEnd(agentId: number) {
  const index = testingAgents.value.indexOf(agentId)
  if (index > -1) {
    testingAgents.value.splice(index, 1)
  }
}
</script>

<style scoped>
.agent-management-page {
  @apply p-6 bg-gray-50 dark:bg-gray-900 min-h-screen;
}

/* 测试状态遮罩效果 */
.agent-management-page.testing {
  @apply relative;
}

.agent-management-page.testing::before {
  content: "";

  @apply absolute inset-0 bg-black bg-opacity-20 z-50 pointer-events-none;

  backdrop-filter: blur(1px);
}

.agent-management-page.testing .page-header,
.agent-management-page.testing .stats-overview,
.agent-management-page.testing .filter-bar {
  @apply relative z-30;
}

.agent-management-page.testing .agent-list-container {
  @apply relative z-30;
}

.page-header {
  @apply flex items-start justify-between mb-6;
}

.header-left {
  @apply space-y-2;
}

.page-title {
  @apply text-2xl font-bold text-gray-900 dark:text-white m-0 flex items-center gap-3;
}

.page-description {
  @apply text-gray-600 dark:text-gray-400;
}

.header-actions {
  @apply flex gap-3;
}

.btn-primary {
  @apply flex items-center gap-2 px-4 py-2 bg-blue-500 hover:bg-blue-600 disabled:bg-blue-300 text-white rounded-lg transition-colors;
}

.btn-secondary {
  @apply flex items-center gap-2 px-4 py-2 bg-white hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg transition-colors;
}

.btn-danger {
  @apply flex items-center gap-2 px-4 py-2 bg-red-500 hover:bg-red-600 disabled:bg-red-300 text-white rounded-lg transition-colors;
}

.stats-overview {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6;
}

.filter-bar {
  @apply flex flex-col lg:flex-row gap-4 lg:items-end justify-between p-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg mb-6;
}

.search-container {
  @apply flex-1 max-w-md;
}

.search-input-wrapper {
  @apply relative;
}

.search-icon {
  @apply absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500;
}

.search-input {
  @apply w-full pl-10 pr-10 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
}

.clear-search-btn {
  @apply absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors;
}

.filter-controls {
  @apply flex flex-wrap gap-4 items-end;
}

.filter-group {
  @apply space-y-1;
}

.filter-label {
  @apply block text-sm font-medium text-gray-700 dark:text-gray-300;
}

.filter-select {
  @apply px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
}

.reset-btn {
  @apply flex items-center gap-1 px-3 py-2 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 disabled:opacity-50 transition-colors;
}

.agent-list-container {
  @apply bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden;
}

.list-header {
  @apply flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 p-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900;
}

.list-info {
  @apply flex items-center gap-4;
}

.result-count {
  @apply text-sm text-gray-600 dark:text-gray-400;
}

.filter-indicator {
  @apply text-blue-600 dark:text-blue-400;
}

.selection-info {
  @apply text-sm text-blue-600 dark:text-blue-400 font-medium;
}

.view-controls {
  @apply flex items-center gap-4;
}

.view-mode-switcher {
  @apply flex rounded-lg border border-gray-300 dark:border-gray-600 overflow-hidden;
}

.view-btn {
  @apply px-3 py-2 text-sm bg-white dark:bg-gray-800 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors;
}

.view-btn.active {
  @apply bg-blue-500 text-white;
}

.sort-controls {
  @apply flex items-center gap-2;
}

.sort-label {
  @apply text-sm text-gray-600 dark:text-gray-400;
}

.sort-select {
  @apply px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white;
}

.sort-order-btn {
  @apply w-8 h-8 rounded bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 flex items-center justify-center text-gray-600 dark:text-gray-400 transition-colors;
}

.agent-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 p-6;
}

.loading-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 p-6;
}

.loading-card {
  @apply h-64 rounded-lg;
}

.loading-skeleton {
  @apply w-full h-full bg-gray-200 dark:bg-gray-700 rounded-lg animate-pulse;
}

.agent-table-container {
  @apply overflow-x-auto;
}

.empty-state {
  @apply col-span-full flex items-center justify-center py-16;
}

.empty-content {
  @apply text-center space-y-4 max-w-md;
}

.empty-icon {
  @apply text-6xl text-gray-400 dark:text-gray-500;
}

.empty-content h3 {
  @apply text-xl font-semibold text-gray-900 dark:text-white;
}

.empty-content p {
  @apply text-gray-600 dark:text-gray-400;
}

.empty-actions {
  @apply flex gap-3 justify-center;
}

.pagination-container {
  @apply flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 p-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900;
}

.pagination-info {
  @apply text-sm text-gray-600 dark:text-gray-400;
}

.pagination-controls {
  @apply flex items-center gap-1;
}

.pagination-btn {
  @apply w-8 h-8 rounded bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 flex items-center justify-center text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white disabled:opacity-50 transition-colors;
}

.page-numbers {
  @apply flex gap-1 mx-2;
}

.page-btn {
  @apply w-8 h-8 rounded bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 flex items-center justify-center text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors;
}

.page-btn.active {
  @apply bg-blue-500 border-blue-500 text-white;
}

.page-size-selector {
  @apply flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400;
}

.page-size-selector select {
  @apply px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white;
}

.dialog-overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50;
}

.dialog {
  @apply bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4;
}

.dialog-header {
  @apply flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700;
}

.dialog-header h4 {
  @apply text-lg font-semibold text-gray-900 dark:text-white m-0;
}

.dialog-close {
  @apply w-8 h-8 rounded-full bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 flex items-center justify-center text-gray-500 dark:text-gray-400 transition-colors;
}

.dialog-content {
  @apply p-4 space-y-3;
}

.dialog-content p {
  @apply text-gray-700 dark:text-gray-300 m-0;
}

.warning-text {
  @apply text-red-600 dark:text-red-400 text-sm;
}

.dialog-actions {
  @apply flex gap-3 p-4 border-t border-gray-200 dark:border-gray-700;
}

.notification {
  @apply fixed top-4 right-4 flex items-center gap-3 px-4 py-3 rounded-lg shadow-lg z-50 transition-all duration-300;
}

.notification.success {
  @apply bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 border border-green-200 dark:border-green-800;
}

.notification.error {
  @apply bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 border border-red-200 dark:border-red-800;
}

.notification.warning {
  @apply bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 border border-yellow-200 dark:border-yellow-800;
}

.notification.info {
  @apply bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 border border-blue-200 dark:border-blue-800;
}

.notification-close {
  @apply w-5 h-5 rounded-full bg-current bg-opacity-20 hover:bg-opacity-30 flex items-center justify-center transition-colors;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes pulse {
  0%,
  100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* 响应式设计 */
@media (width <= 640px) {
  .agent-management-page {
    @apply p-4;
  }

  .page-header {
    @apply flex-col items-stretch gap-4;
  }

  .header-actions {
    @apply flex-col sm:flex-row;
  }

  .stats-overview {
    @apply grid-cols-2;
  }

  .filter-bar {
    @apply flex-col items-stretch;
  }

  .filter-controls {
    @apply flex-col items-stretch;
  }

  .agent-grid {
    @apply grid-cols-1 p-4;
  }

  .pagination-container {
    @apply flex-col items-stretch;
  }
}
</style>

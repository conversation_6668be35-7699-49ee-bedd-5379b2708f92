package com.dipspro.modules.normalize.service;

import java.util.List;
import java.util.Map;
import java.util.Optional;

import com.dipspro.modules.chat.entity.IndexDefinition;

/**
 * 指标服务接口
 * 提供指标定义的查询功能
 */
public interface IndexService {

    /**
     * 初始化指标缓存
     */
    void initIndexCache();

    /**
     * 获取所有指标定义
     * @return 指标定义列表
     */
    List<IndexDefinition> getAllIndexDefinitions();

    /**
     * 根据planar指标代码查询指标定义
     * @param planarIndexCode planar指标代码
     * @return 指标定义（可能为空）
     */
    Optional<IndexDefinition> getIndexByPlanarCode(String planarIndexCode);

    /**
     * 根据遗留指标代码查询指标定义
     * @param legacyIndexCode 遗留指标代码
     * @return 指标定义（可能为空）
     */
    Optional<IndexDefinition> getIndexByLegacyCode(String legacyIndexCode);

    /**
     * 获取所有主指标
     * @return 主指标列表
     */
    List<IndexDefinition> getAllMainIndexes();

    /**
     * 获取指定主指标的所有子指标
     * @param mainIndexCode 主指标代码
     * @return 子指标列表
     */
    List<IndexDefinition> getSubIndexes(String mainIndexCode);

    /**
     * 获取主指标与子指标的映射关系
     * @return 主指标代码 -> 子指标列表的映射
     */
    Map<String, List<IndexDefinition>> getMainToSubIndexMapping();
} 
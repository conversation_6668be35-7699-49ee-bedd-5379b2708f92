package com.dipspro.modules.embedding.service;

import com.dipspro.DipsProApp; // 添加正确的导入
import com.dipspro.modules.embedding.service.impl.Text2VecEmbeddingService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest; // 添加导入
import org.springframework.beans.factory.annotation.Autowired; // 添加导入

import java.util.ArrayList;
import java.util.List;

/**
 * Text2VecEmbeddingService测试类
 */
@SpringBootTest(classes = DipsProApp.class)
public class Text2VecEmbeddingServiceTest {

    @Autowired // 使用正确的注解
    private Text2VecEmbeddingService embeddingService;
    private List<String> testList = new ArrayList<>();

    @BeforeEach
    public void setup() {
        // 如果服务需要初始化，确保在这里调用
        if (embeddingService != null && !embeddingService.isInitialized()) {
            embeddingService.init();
        }
        // 放入3个测试文本
        testList.add("楼盘档案：北京万科广场");
        testList.add("楼盘画像：北京万科广场");
        testList.add("北京万科的画像");
    }

    @Test
    public void testCosineSimilarity() {
        // embedding
        List<float[]> eList = embeddingService.encodeBatch(testList);
        // 句子间相互的相似度
        for (int i = 0; i < eList.size(); i++) {
            for (int j = i + 1; j < eList.size(); j++) {
                float[] vector1 = eList.get(i);
                float[] vector2 = eList.get(j);
                double similarity = embeddingService.cosineSimilarity(vector1, vector2);
                System.out.println("Cosine similarity between vectors " + i + " and " + j + ": " + similarity);
            }
        }
    }
}
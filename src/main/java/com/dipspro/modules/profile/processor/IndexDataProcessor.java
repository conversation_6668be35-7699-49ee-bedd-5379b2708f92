package com.dipspro.modules.profile.processor;

import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.dipspro.modules.chat.entity.IndexDefinition;
import com.dipspro.modules.normalize.dto.NormalizeData;
import com.dipspro.modules.normalize.service.DataMigrationService;
import com.dipspro.modules.normalize.service.IndexService;
import com.dipspro.modules.profile.dto.IndexWithScore;
import com.dipspro.modules.profile.dto.MobileCombinedStatsDTO;
import com.dipspro.modules.profile.dto.ProjectDetail;
import com.dipspro.util.MaskUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * 指标数据处理器
 * 
 * <AUTHOR>
 * @since 2025-06-03 14:51:45
 */
@Slf4j
@Component
public class IndexDataProcessor {

    @Autowired
    private IndexService indexService;

    @Autowired
    private DataMigrationService dataMigrationService;

    /**
     * 为项目列表处理指标数据
     * 
     * @param projects      项目列表
     * @param combinedStats 综合统计数据
     * @param mobile        手机号
     */
    public void processProjectsIndexData(List<ProjectDetail> projects, MobileCombinedStatsDTO combinedStats,
            String mobile) {
        log.info("开始查询指标数据，手机号: {}", MaskUtil.maskMobile(mobile));

        // 1. 获取所有指标定义
        List<IndexDefinition> allIndexDefinitions = indexService.getAllIndexDefinitions();
        Map<String, IndexDefinition> indexDefinitionMap = allIndexDefinitions.stream()
                .collect(Collectors.toMap(definition -> definition.getLegacyIndexCode(), definition -> definition,
                        (v1, v2) -> v1));

        // 缓存父子关系
        Map<String, List<IndexDefinition>> parentChildMap = createParentChildMap(allIndexDefinitions);

        // 2. 获取并处理数据集
        if (combinedStats.getDatasetStats() != null && combinedStats.getDatasetStats().getTotal() > 0) {
            List<NormalizeData> dataDtos = dataMigrationService.queryDataset(mobile);

            // 将数据按customer_project分组
            Map<String, List<NormalizeData>> projectDataMap = dataDtos.stream()
                    .filter(dto -> dto.getData() != null && dto.getData().containsKey("customer_project"))
                    .collect(Collectors.groupingBy(
                            dto -> String.valueOf(dto.getData().get("customer_project")),
                            Collectors.toList()));

            log.debug("按customer_project分组后的数据集: {}", projectDataMap.keySet());

            // 处理每个项目的详细数据
            for (ProjectDetail project : projects) {
                String customerProject = project.getCustomerProject();
                if (projectDataMap.containsKey(customerProject)) {
                    List<NormalizeData> projectRecords = projectDataMap.get(customerProject);

                    // 获取最新记录
                    Optional<NormalizeData> latestRecord = getLatestRecordWithDataPeriod(projectRecords);

                    if (latestRecord.isPresent()) {
                        processProjectIndexData(project, latestRecord.get().getData(), indexDefinitionMap,
                                parentChildMap);
                    } else {
                        log.debug("项目[{}]在归一数据中未找到有效的时间记录", customerProject);
                    }
                } else {
                    log.debug("项目[{}]在归一数据中未找到记录", customerProject);
                }
            }
        } else {
            log.info("手机号: {}, 没有找到归一数据", MaskUtil.maskMobile(mobile));
        }
    }

    /**
     * 处理单个项目的指标数据
     * 
     * @param project            项目详情
     * @param data               数据
     * @param indexDefinitionMap 指标定义映射
     * @param parentChildMap     父子关系映射
     */
    public void processProjectIndexData(ProjectDetail project,
            Map<String, Object> data,
            Map<String, IndexDefinition> indexDefinitionMap,
            Map<String, List<IndexDefinition>> parentChildMap) {
        log.debug("处理项目[{}]的指标数据", project.getCustomerProject());

        // 清空之前的指标数据
        project.getIndexScores().clear();
        project.getHighScoreIndices().clear();
        project.getLowScoreIndices().clear();
        project.getVeryLowScoreIndices().clear();
        project.getMainLowScoreIndices().clear();
        project.getSubLowScoreIndices().clear();

        // 1. 提取以r开头的指标数据，且值不为空
        List<Map.Entry<String, Object>> indexEntries = data.entrySet().stream()
                .filter(entry -> entry.getKey().startsWith("r") && entry.getKey().length() > 1
                        && entry.getValue() != null
                        && !(entry.getValue() instanceof String && (((String) entry.getValue()).isEmpty()
                                || ((String) entry.getValue()).equalsIgnoreCase("null"))))
                .collect(Collectors.toList());

        // 2. 处理指标数据
        for (Map.Entry<String, Object> entry : indexEntries) {
            String indexCode = entry.getKey();
            Object scoreObj = entry.getValue();

            // 解析指标得分
            int score = parseScore(scoreObj);

            // 查找指标定义
            IndexDefinition definition = indexDefinitionMap.get(indexCode);
            if (definition == null) {
                log.debug("未找到指标[{}]的定义", indexCode);
                continue;
            }

            // 创建带分数的指标
            IndexWithScore indexWithScore = IndexWithScore.fromDefinition(definition, score);

            // 添加到项目的指标列表
            project.getIndexScores().add(indexWithScore);

            // 根据分数对指标进行分组
            if (score != -1) { // 忽略未评分的指标
                if (indexWithScore.isHighScore()) {
                    project.getHighScoreIndices().add(indexWithScore);
                } else if (indexWithScore.isLowScore()) {
                    project.getLowScoreIndices().add(indexWithScore);

                    // 如果是主指标，添加到低分主指标列表
                    if (definition.isMainIndex()) {
                        project.getMainLowScoreIndices().add(indexWithScore);
                    }
                } else if (indexWithScore.isVeryLowScore()) {
                    project.getVeryLowScoreIndices().add(indexWithScore);

                    // 如果是主指标，添加到低分主指标列表
                    if (definition.isMainIndex()) {
                        project.getMainLowScoreIndices().add(indexWithScore);
                    }
                }
            }
        }

        // 3. 特别处理低分/极低分的主指标的子指标
        for (IndexWithScore mainIndex : project.getMainLowScoreIndices()) {
            // 查找主指标的子指标
            List<IndexDefinition> childIndices = parentChildMap.get(mainIndex.getIndexCode());
            if (childIndices != null) {
                for (IndexDefinition childDef : childIndices) {
                    // 在已处理的指标中查找子指标
                    Optional<IndexWithScore> childWithScore = project.getIndexScores().stream()
                            .filter(idx -> idx.getIndexCode().equals(childDef.getPlanarIndexCode()))
                            .findFirst();

                    // 如果找到了子指标，添加到子指标列表
                    childWithScore.ifPresent(index -> {
                        if (!index.isNotRated() && (index.isLowScore() || index.isVeryLowScore())) {
                            project.getSubLowScoreIndices().add(index);
                        }
                    });
                }
            }
        }

        // 根据得分排序：低分在前，高分在后
        Comparator<IndexWithScore> scoreComparator = Comparator.comparingInt(index -> index.getScore());

        project.getMainLowScoreIndices().sort(scoreComparator);
        project.getSubLowScoreIndices().sort(scoreComparator);

        log.info("项目[{}]的指标数据统计: 总指标数={}, 高分指标数={}, 低分指标数={}, 极低分指标数={}, 低分主指标数={}, 低分子指标数={}",
                project.getCustomerProject(),
                project.getIndexScores().size(),
                project.getHighScoreIndices().size(),
                project.getLowScoreIndices().size(),
                project.getVeryLowScoreIndices().size(),
                project.getMainLowScoreIndices().size(),
                project.getSubLowScoreIndices().size());
    }

    /**
     * 解析指标得分
     * 
     * @param scoreObj 得分对象
     * @return 解析后的整数得分，如果无法解析则返回-1
     */
    public int parseScore(Object scoreObj) {
        if (scoreObj == null) {
            return -1;
        }

        // 尝试将得分转换为整数
        try {
            if (scoreObj instanceof Number) {
                return ((Number) scoreObj).intValue();
            } else if (scoreObj instanceof String) {
                String scoreStr = (String) scoreObj;
                if (scoreStr.isEmpty() || scoreStr.equalsIgnoreCase("null")) {
                    return -1;
                }
                return Integer.parseInt(scoreStr);
            }
        } catch (NumberFormatException e) {
            log.debug("无法解析指标得分: {}", scoreObj);
        }

        return -1;
    }

    /**
     * 创建指标父子关系映射
     * 
     * @param allIndexDefinitions 所有指标定义
     * @return 父指标到子指标的映射
     */
    public Map<String, List<IndexDefinition>> createParentChildMap(List<IndexDefinition> allIndexDefinitions) {
        Map<String, List<IndexDefinition>> parentChildMap = new HashMap<>();

        for (IndexDefinition definition : allIndexDefinitions) {
            String parentCode = definition.getParentIndexCode();
            if (parentCode != null && !parentCode.isEmpty()) {
                parentChildMap.computeIfAbsent(parentCode, k -> new java.util.ArrayList<>()).add(definition);
            }
        }

        return parentChildMap;
    }

    /**
     * 获取最新的带有data_period的记录
     * 
     * @param records 记录列表
     * @return 最新记录
     */
    private Optional<NormalizeData> getLatestRecordWithDataPeriod(List<NormalizeData> records) {
        return records.stream()
                .filter(record -> record.getData() != null && record.getData().containsKey("data_period"))
                .max(Comparator.comparing(record -> {
                    String dataPeriod = String.valueOf(record.getData().get("data_period"));
                    // 尝试解析日期，格式可能是yyyy-MM-dd或yyyy-MM
                    try {
                        // 如果是yyyy-MM格式，转换为yyyy-MM-01
                        if (dataPeriod.matches("\\d{4}-\\d{2}")) {
                            dataPeriod += "-01";
                        }
                        return java.time.LocalDate.parse(dataPeriod);
                    } catch (Exception e) {
                        // 日期解析失败，返回最小日期作为默认值
                        log.debug("日期[{}]解析失败: {}", dataPeriod, e.getMessage());
                        return java.time.LocalDate.MIN;
                    }
                }));
    }
}